<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-paas-appframework</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.5-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-paas-app-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-expression</artifactId>
        </dependency>
        <!-- 元数据的依赖 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-metadata-provider</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fxiaoke</groupId>
                    <artifactId>i18n-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>i18n-util</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>metrics-oss</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>okhttp</artifactId>
                    <groupId>com.squareup.okhttp</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fxiaoke.common</groupId>
                    <artifactId>metrics-oss</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-fsi-proxy</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-framework</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.colin-lee</groupId>
                    <artifactId>mybatis-spring-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-app-expression</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-expression</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-jaxrs</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-mapper-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-xc</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>classgraph</artifactId>
                    <groupId>io.github.classgraph</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-paas-app-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.ahocorasick</groupId>
                    <artifactId>ahocorasick</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>cglib</artifactId>
                    <groupId>cglib</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-polling-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>jdbc-support</artifactId>

        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mybatis-spring-support</artifactId>

        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-rest-client-common-api</artifactId>
            <version>${restproxy.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-metadata-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.checkerframework/checker-qual -->
        <dependency>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.errorprone/error_prone_annotations -->
        <dependency>
            <groupId>com.google.errorprone</groupId>
            <artifactId>error_prone_annotations</artifactId>
        </dependency>
        <!--网盘相关 start-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-netdisk-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--网盘相关 end-->
        <!--埋点工具包-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-bpm-server-operate-report</artifactId>
            <version>6.3.0-SNAPSHOT</version>
        </dependency>
        <!-- 审计日志包 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>biz-log-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>
        <!-- 企业互联接口 -->
        <!--        <dependency>-->
        <!--            <groupId>com.fxiaoke</groupId>-->
        <!--            <artifactId>fs-enterpriserelation-rest-api</artifactId>-->
        <!--            <version>2.0.0-SNAPSHOT</version>-->
        <!--        </dependency>-->
        <!-- 企业互联接口 -->
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-enterpriserelation-rest-api2</artifactId>
        </dependency>
        <!-- 组织架构相关 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-api</artifactId>
        </dependency>
        <!-- token 服务-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-token</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>

        <!-- 图片打包dubbo接口 -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>warehouse-batch-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>

        <!-- html解析工具 -->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.3</version>
        </dependency>

        <!-- 限速 -->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>limit-support</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- 用户登陆服务 Client -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-user-login-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- 文件系统服务 Client -->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsc-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <!-- 营销通 -->
        <dependency>
            <groupId>com.facishare.marketing</groupId>
            <artifactId>fs-marketing-outapi</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml</groupId>
                    <artifactId>classmate</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.esotericsoftware</groupId>
                    <artifactId>reflectasm</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>javax.ws.rs-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>compile</scope>
        </dependency>

        <!-- XML 处理依赖 -->
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>xml-apis</artifactId>
                    <groupId>xml-apis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>*</include>
                    <include>**/*</include>
                </includes>
                <!-- 编译时不编译i18n.csv，防止打成jar包 -->
                <excludes>
                    <exclude>language/*</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>JaCoCo Agent</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>
                                prepare-agent
                            </goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>JaCoCo Report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>
                                report
                            </goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.9.1.2184</version>
            </plugin>
        </plugins>    
    </build>
</project>
