package com.facishare.paas.appframework.common.model.enums

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：ObjectActionInfo模型类的单元测试
 */
class ObjectActionInfoTest extends Specification {

    def "test ObjectActionInfo getter and setter methods"() {
        given: "创建ObjectActionInfo实例"
        def objectActionInfo = new ObjectActionInfo()

        when: "设置各个属性"
        objectActionInfo.setActionCode("ADD")
        objectActionInfo.setActionLabelKey("action.add")
        objectActionInfo.setDefaultActionLabel("Add")
        objectActionInfo.setButtonApiName("add_button")
        objectActionInfo.setActionLabel("添加")

        then: "验证getter方法返回正确的值"
        objectActionInfo.getActionCode() == "ADD"
        objectActionInfo.getActionLabelKey() == "action.add"
        objectActionInfo.getDefaultActionLabel() == "Add"
        objectActionInfo.getButtonApiName() == "add_button"
        objectActionInfo.getActionLabel() == "添加"
    }

    def "test ObjectActionInfo with null values"() {
        given: "创建ObjectActionInfo实例"
        def objectActionInfo = new ObjectActionInfo()

        when: "设置null值"
        objectActionInfo.setActionCode(null)
        objectActionInfo.setActionLabelKey(null)
        objectActionInfo.setDefaultActionLabel(null)
        objectActionInfo.setButtonApiName(null)
        objectActionInfo.setActionLabel(null)

        then: "验证getter方法返回null"
        objectActionInfo.getActionCode() == null
        objectActionInfo.getActionLabelKey() == null
        objectActionInfo.getDefaultActionLabel() == null
        objectActionInfo.getButtonApiName() == null
        objectActionInfo.getActionLabel() == null
    }

    def "test ObjectActionInfo with empty strings"() {
        given: "创建ObjectActionInfo实例"
        def objectActionInfo = new ObjectActionInfo()

        when: "设置空字符串"
        objectActionInfo.setActionCode("")
        objectActionInfo.setActionLabelKey("")
        objectActionInfo.setDefaultActionLabel("")
        objectActionInfo.setButtonApiName("")
        objectActionInfo.setActionLabel("")

        then: "验证getter方法返回空字符串"
        objectActionInfo.getActionCode() == ""
        objectActionInfo.getActionLabelKey() == ""
        objectActionInfo.getDefaultActionLabel() == ""
        objectActionInfo.getButtonApiName() == ""
        objectActionInfo.getActionLabel() == ""
    }

    def "test ObjectActionInfo equals and hashCode"() {
        given: "创建两个相同的ObjectActionInfo实例"
        def info1 = new ObjectActionInfo()
        info1.setActionCode("EDIT")
        info1.setActionLabelKey("action.edit")
        info1.setDefaultActionLabel("Edit")
        info1.setButtonApiName("edit_button")
        info1.setActionLabel("编辑")

        def info2 = new ObjectActionInfo()
        info2.setActionCode("EDIT")
        info2.setActionLabelKey("action.edit")
        info2.setDefaultActionLabel("Edit")
        info2.setButtonApiName("edit_button")
        info2.setActionLabel("编辑")

        when: "比较两个实例"
        def areEqual = info1.equals(info2)
        def hashCodesEqual = info1.hashCode() == info2.hashCode()

        then: "应该相等且hashCode相同"
        areEqual == true
        hashCodesEqual == true
    }

    def "test ObjectActionInfo toString method"() {
        given: "创建ObjectActionInfo实例并设置属性"
        def objectActionInfo = new ObjectActionInfo()
        objectActionInfo.setActionCode("DELETE")
        objectActionInfo.setActionLabelKey("action.delete")
        objectActionInfo.setDefaultActionLabel("Delete")
        objectActionInfo.setButtonApiName("delete_button")
        objectActionInfo.setActionLabel("删除")

        when: "调用toString方法"
        def toStringResult = objectActionInfo.toString()

        then: "toString结果应该包含所有属性信息"
        toStringResult.contains("DELETE")
        toStringResult.contains("action.delete")
        toStringResult.contains("Delete")
        toStringResult.contains("delete_button")
        toStringResult.contains("删除")
    }
}
