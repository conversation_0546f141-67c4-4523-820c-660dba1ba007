package com.facishare.paas.appframework.common.service.model

import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：ManageGroupType枚举类的单元测试
 */
class ManageGroupTypeTest extends Specification {

    def "test all enum values exist"() {
        expect: "验证所有枚举值都存在"
        ManageGroupType.OBJECT != null
        ManageGroupType.LAYOUT != null
        ManageGroupType.DUPLICATE_SEARCH_RULE != null
        ManageGroupType.SCORE_RULE != null
        ManageGroupType.BUSINESS_ROLE != null
        ManageGroupType.LEADS_POOL_RULE != null
    }

    @Unroll
    def "test getType method returns correct value for #enumValue"() {
        expect: "验证getType方法返回正确的类型值"
        enumValue.getType() == expectedType

        where:
        enumValue                           | expectedType
        ManageGroupType.OBJECT              | "obj"
        ManageGroupType.LAYOUT              | "obj_layout"
        ManageGroupType.DUPLICATE_SEARCH_RULE | "obj_duplicate"
        ManageGroupType.SCORE_RULE          | "obj_score"
        ManageGroupType.BUSINESS_ROLE       | "businessRole"
        ManageGroupType.LEADS_POOL_RULE     | "leadsPoolRule"
    }

    def "test enum values method returns all values"() {
        when: "调用values方法"
        def values = ManageGroupType.values()

        then: "应该返回所有6个枚举值"
        values.length == 6
        values.contains(ManageGroupType.OBJECT)
        values.contains(ManageGroupType.LAYOUT)
        values.contains(ManageGroupType.DUPLICATE_SEARCH_RULE)
        values.contains(ManageGroupType.SCORE_RULE)
        values.contains(ManageGroupType.BUSINESS_ROLE)
        values.contains(ManageGroupType.LEADS_POOL_RULE)
    }

    @Unroll
    def "test valueOf method returns correct enum for #typeName"() {
        when: "使用valueOf方法"
        def result = ManageGroupType.valueOf(typeName)

        then: "应该返回正确的枚举值"
        result == expectedEnum

        where:
        typeName                | expectedEnum
        "OBJECT"               | ManageGroupType.OBJECT
        "LAYOUT"               | ManageGroupType.LAYOUT
        "DUPLICATE_SEARCH_RULE" | ManageGroupType.DUPLICATE_SEARCH_RULE
        "SCORE_RULE"           | ManageGroupType.SCORE_RULE
        "BUSINESS_ROLE"        | ManageGroupType.BUSINESS_ROLE
        "LEADS_POOL_RULE"      | ManageGroupType.LEADS_POOL_RULE
    }

    def "test valueOf with invalid name throws exception"() {
        when: "使用无效的枚举名称"
        ManageGroupType.valueOf("INVALID_TYPE")

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test enum name method returns correct name"() {
        expect: "验证name方法返回正确的枚举名称"
        ManageGroupType.OBJECT.name() == "OBJECT"
        ManageGroupType.LAYOUT.name() == "LAYOUT"
        ManageGroupType.DUPLICATE_SEARCH_RULE.name() == "DUPLICATE_SEARCH_RULE"
        ManageGroupType.SCORE_RULE.name() == "SCORE_RULE"
        ManageGroupType.BUSINESS_ROLE.name() == "BUSINESS_ROLE"
        ManageGroupType.LEADS_POOL_RULE.name() == "LEADS_POOL_RULE"
    }

    def "test enum toString method"() {
        expect: "验证toString方法返回枚举名称"
        ManageGroupType.OBJECT.toString() == "OBJECT"
        ManageGroupType.LAYOUT.toString() == "LAYOUT"
        ManageGroupType.DUPLICATE_SEARCH_RULE.toString() == "DUPLICATE_SEARCH_RULE"
        ManageGroupType.SCORE_RULE.toString() == "SCORE_RULE"
        ManageGroupType.BUSINESS_ROLE.toString() == "BUSINESS_ROLE"
        ManageGroupType.LEADS_POOL_RULE.toString() == "LEADS_POOL_RULE"
    }
}
