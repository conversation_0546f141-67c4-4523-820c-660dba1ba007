package com.facishare.paas.appframework.common.service.dto

import com.google.common.collect.Maps
import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：InternationalItem国际化信息数据对象的单元测试
 */
class InternationalItemTest extends Specification {

    def "test default constructor"() {
        when: "创建InternationalItem实例"
        def item = new InternationalItem()

        then: "默认值应该为null"
        item.internationalKey == null
        item.internationalParameters == null
        item.defaultParameterValues == null
    }

    def "test all args constructor"() {
        given: "准备构造参数"
        def key = "test.key"
        def parameters = ["param1", "param2"]
        def defaultValues = ["key1": "value1", "key2": "value2"]

        when: "使用全参构造函数"
        def item = new InternationalItem(key, parameters, defaultValues)

        then: "所有字段应该正确设置"
        item.internationalKey == key
        item.internationalParameters == parameters
        item.defaultParameterValues == defaultValues
    }

    def "test builder pattern"() {
        when: "使用builder创建实例"
        def item = InternationalItem.builder()
                .internationalKey("test.builder.key")
                .internationalParameters(["param1", "param2"])
                .defaultParameterValues(["key": "value"])
                .build()

        then: "所有字段应该正确设置"
        item.internationalKey == "test.builder.key"
        item.internationalParameters.size() == 2
        item.internationalParameters.contains("param1")
        item.internationalParameters.contains("param2")
        item.defaultParameterValues.get("key") == "value"
    }

    def "test getter and setter methods"() {
        given: "创建InternationalItem实例"
        def item = new InternationalItem()

        when: "设置各个属性"
        item.setInternationalKey("setter.key")
        item.setInternationalParameters(["param1"])
        item.setDefaultParameterValues(["default": "value"])

        then: "getter方法应该返回正确的值"
        item.getInternationalKey() == "setter.key"
        item.getInternationalParameters().size() == 1
        item.getInternationalParameters().get(0) == "param1"
        item.getDefaultParameterValues().get("default") == "value"
    }

    def "test with null values"() {
        given: "创建InternationalItem实例"
        def item = new InternationalItem()

        when: "设置null值"
        item.setInternationalKey(null)
        item.setInternationalParameters(null)
        item.setDefaultParameterValues(null)

        then: "应该能够设置null值"
        item.getInternationalKey() == null
        item.getInternationalParameters() == null
        item.getDefaultParameterValues() == null
    }

    def "test with empty collections"() {
        given: "创建InternationalItem实例"
        def item = new InternationalItem()

        when: "设置空集合"
        item.setInternationalKey("")
        item.setInternationalParameters([])
        item.setDefaultParameterValues([:])

        then: "应该能够设置空集合"
        item.getInternationalKey() == ""
        item.getInternationalParameters().isEmpty()
        item.getDefaultParameterValues().isEmpty()
    }

    def "test equals and hashCode"() {
        given: "创建两个相同的InternationalItem实例"
        def item1 = InternationalItem.builder()
                .internationalKey("test.key")
                .internationalParameters(["param1", "param2"])
                .defaultParameterValues(["key": "value"])
                .build()

        def item2 = InternationalItem.builder()
                .internationalKey("test.key")
                .internationalParameters(["param1", "param2"])
                .defaultParameterValues(["key": "value"])
                .build()

        when: "比较两个实例"
        def areEqual = item1.equals(item2)
        def hashCodesEqual = item1.hashCode() == item2.hashCode()

        then: "应该相等且hashCode相同"
        areEqual == true
        hashCodesEqual == true
    }

    def "test not equals with different keys"() {
        given: "创建两个不同的InternationalItem实例"
        def item1 = InternationalItem.builder()
                .internationalKey("test.key1")
                .build()

        def item2 = InternationalItem.builder()
                .internationalKey("test.key2")
                .build()

        when: "比较两个实例"
        def areEqual = item1.equals(item2)

        then: "应该不相等"
        areEqual == false
    }

    def "test toString method"() {
        given: "创建InternationalItem实例"
        def item = InternationalItem.builder()
                .internationalKey("test.toString.key")
                .internationalParameters(["param1"])
                .defaultParameterValues(["key": "value"])
                .build()

        when: "调用toString方法"
        def toStringResult = item.toString()

        then: "toString结果应该包含相关信息"
        toStringResult != null
        toStringResult.contains("test.toString.key")
    }

    def "test builder with partial data"() {
        when: "使用builder只设置部分字段"
        def item = InternationalItem.builder()
                .internationalKey("partial.key")
                .build()

        then: "设置的字段应该有值，未设置的应该为null"
        item.internationalKey == "partial.key"
        item.internationalParameters == null
        item.defaultParameterValues == null
    }

    def "test mutable collections"() {
        given: "创建InternationalItem实例"
        def item = InternationalItem.builder()
                .internationalParameters(["initial"])
                .defaultParameterValues(["initial": "value"])
                .build()

        when: "修改集合内容"
        item.internationalParameters.add("added")
        item.defaultParameterValues.put("added", "addedValue")

        then: "集合内容应该被修改"
        item.internationalParameters.size() == 2
        item.internationalParameters.contains("initial")
        item.internationalParameters.contains("added")
        item.defaultParameterValues.size() == 2
        item.defaultParameterValues.get("initial") == "value"
        item.defaultParameterValues.get("added") == "addedValue"
    }

    def "test with complex parameter values"() {
        given: "创建包含复杂参数的InternationalItem"
        def complexParameters = ["param.with.dots", "param_with_underscores", "paramWithCamelCase"]
        def complexDefaultValues = Maps.newHashMap()
        complexDefaultValues.put("complex.key.1", "复杂值1")
        complexDefaultValues.put("complex.key.2", "Complex Value 2")
        complexDefaultValues.put("complex.key.3", "123")

        when: "创建实例"
        def item = InternationalItem.builder()
                .internationalKey("complex.test.key")
                .internationalParameters(complexParameters)
                .defaultParameterValues(complexDefaultValues)
                .build()

        then: "所有复杂数据应该正确设置"
        item.internationalKey == "complex.test.key"
        item.internationalParameters.size() == 3
        item.internationalParameters.containsAll(complexParameters)
        item.defaultParameterValues.size() == 3
        item.defaultParameterValues.get("complex.key.1") == "复杂值1"
        item.defaultParameterValues.get("complex.key.2") == "Complex Value 2"
        item.defaultParameterValues.get("complex.key.3") == "123"
    }
}
