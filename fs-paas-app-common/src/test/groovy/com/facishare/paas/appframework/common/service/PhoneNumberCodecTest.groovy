package com.facishare.paas.appframework.common.service

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation
import com.facishare.rest.core.codec.DefaultRestCodec
import spock.lang.Specification
import spock.lang.Unroll

import java.nio.charset.Charset

/**
 * GenerateByAI
 * 测试内容描述：PhoneNumberCodec编解码器的单元测试
 */
class PhoneNumberCodecTest extends Specification {

    def phoneNumberCodec = new PhoneNumberCodec()



    def "test decodeResult with JSON array response"() {
        given: "准备JSON数组响应"
        def result1 = new QueryPhoneNumberInformation.Result()
        result1.setMobile("13800138000")
        result1.setProvince("北京")
        result1.setCity("北京")
        
        def result2 = new QueryPhoneNumberInformation.Result()
        result2.setMobile("13900139000")
        result2.setProvince("上海")
        result2.setCity("上海")
        
        def jsonArray = JSON.toJSONString([result1, result2])
        def bytes = jsonArray.getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def result = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回包含多个结果的Code对象"
        result instanceof QueryPhoneNumberInformation.Code
        result.getErrCode() == 200
        result.getResults().size() == 2
        result.getResults()[0].getMobile() == "13800138000"
        result.getResults()[1].getMobile() == "13900139000"
    }

    def "test decodeResult with JSON object response"() {
        given: "准备JSON对象响应"
        def result = new QueryPhoneNumberInformation.Result()
        result.setMobile("13800138000")
        result.setProvince("北京")
        result.setCity("北京")
        result.setOperator("移动")
        
        def jsonObject = JSON.toJSONString(result)
        def bytes = jsonObject.getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def result2 = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回包含单个结果的Code对象"
        result2 instanceof QueryPhoneNumberInformation.Code
        result2.getErrCode() == 200
        result2.getResults().size() == 1
        result2.getResults()[0].getMobile() == "13800138000"
        result2.getResults()[0].getProvince() == "北京"
        result2.getResults()[0].getOperator() == "移动"
    }

    def "test decodeResult with plain text response"() {
        given: "准备纯文本响应"
        def plainText = "Error: Invalid phone number"
        def bytes = plainText.getBytes(Charset.forName("UTF-8"))
        def statusCode = 400
        def headers = [:]

        when: "调用decodeResult方法"
        def result = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回包含错误信息的Code对象"
        result instanceof QueryPhoneNumberInformation.Code
        result.getErrCode() == 400
        result.getErrMessage() == plainText
        result.getResults() == null
    }

    def "test decodeResult with empty response"() {
        given: "准备空响应"
        def bytes = "".getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def result = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回null"
        result == null
    }



    @Unroll
    def "test decodeResult with different status codes: #statusCode"() {
        given: "准备测试数据"
        def result = new QueryPhoneNumberInformation.Result()
        result.setMobile("13800138000")
        
        def jsonObject = JSON.toJSONString(result)
        def bytes = jsonObject.getBytes(Charset.forName("UTF-8"))
        def headers = [:]

        when: "调用decodeResult方法"
        def decodedResult = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回正确的状态码"
        decodedResult instanceof QueryPhoneNumberInformation.Code
        decodedResult.getErrCode() == statusCode

        where:
        statusCode << [200, 201, 400, 404, 500]
    }

    def "test decodeResult with malformed JSON"() {
        given: "准备格式错误的JSON"
        def malformedJson = '{"mobile":"13800138000","province":'  // 不完整的JSON
        def bytes = malformedJson.getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def result = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回null（异常被捕获）"
        result == null
    }

    def "test decodeResult with complex JSON array"() {
        given: "准备复杂的JSON数组"
        def results = []
        for (int i = 0; i < 5; i++) {
            def result = new QueryPhoneNumberInformation.Result()
            result.setMobile("1380013800${i}")
            result.setProvince("省份${i}")
            result.setCity("城市${i}")
            result.setOperator("运营商${i}")
            results.add(result)
        }
        
        def jsonArray = JSON.toJSONString(results)
        def bytes = jsonArray.getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def result = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该正确解析所有结果"
        result instanceof QueryPhoneNumberInformation.Code
        result.getErrCode() == 200
        result.getResults().size() == 5
        result.getResults().each { phoneResult ->
            assert phoneResult.getProvince().startsWith("省份")
            assert phoneResult.getCity().startsWith("城市")
            assert phoneResult.getOperator().startsWith("运营商")
        }
    }

    def "test decodeResult with special characters"() {
        given: "准备包含特殊字符的JSON"
        def result = new QueryPhoneNumberInformation.Result()
        result.setMobile("13800138000")
        result.setProvince("北京市")
        result.setCity("朝阳区")
        result.setOperator("中国移动")
        
        def jsonObject = JSON.toJSONString(result)
        def bytes = jsonObject.getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def decodedResult = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该正确处理中文字符"
        decodedResult instanceof QueryPhoneNumberInformation.Code
        decodedResult.getResults().size() == 1
        decodedResult.getResults()[0].getProvince() == "北京市"
        decodedResult.getResults()[0].getCity() == "朝阳区"
        decodedResult.getResults()[0].getOperator() == "中国移动"
    }

    def "test decodeResult with different charset"() {
        given: "准备不同编码的响应"
        def result = new QueryPhoneNumberInformation.Result()
        result.setMobile("13800138000")
        result.setProvince("测试省份")
        
        def jsonObject = JSON.toJSONString(result)
        def bytes = jsonObject.getBytes("GBK")  // 使用GBK编码
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def decodedResult = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "由于使用UTF-8解码GBK编码的数据，可能出现乱码或解析失败"
        // 这个测试主要验证编码处理的健壮性
        decodedResult == null || decodedResult instanceof QueryPhoneNumberInformation.Code
    }

    def "test decodeResult with empty JSON array"() {
        given: "准备空JSON数组"
        def jsonArray = "[]"
        def bytes = jsonArray.getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def result = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回包含空结果列表的Code对象"
        result instanceof QueryPhoneNumberInformation.Code
        result.getErrCode() == 200
        result.getResults().isEmpty()
    }

    def "test decodeResult with empty JSON object"() {
        given: "准备空JSON对象"
        def jsonObject = "{}"
        def bytes = jsonObject.getBytes(Charset.forName("UTF-8"))
        def statusCode = 200
        def headers = [:]

        when: "调用decodeResult方法"
        def result = phoneNumberCodec.decodeResult(statusCode, headers, bytes, QueryPhoneNumberInformation.Code.class)

        then: "应该返回包含一个空结果的Code对象"
        result instanceof QueryPhoneNumberInformation.Code
        result.getErrCode() == 200
        result.getResults().size() == 1
        result.getResults()[0] != null
    }

    def cleanup() {
        // 清理元类修改
        DefaultRestCodec.metaClass = null
    }
}
