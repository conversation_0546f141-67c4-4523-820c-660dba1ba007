//package com.facishare.paas.appframework.common.service
//
//import com.facishare.paas.appframework.common.service.dto.QueryDeptByName
import java.lang.reflect.Field
//import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds
//import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds
//import com.facishare.paas.appframework.common.service.dto.QueryGroupByIds
//import com.facishare.paas.appframework.common.service.dto.UserInfo
//import com.facishare.paas.appframework.core.model.User
//import com.facishare.paas.appframework.core.util.RestUtils
//import com.fxiaoke.i18n.client.I18nClient
//import com.fxiaoke.i18n.client.impl.I18nServiceImpl

import org.powermock.reflect.Whitebox
//import spock.lang.Specification
//
//import java.lang.reflect.Method
//
///**
// * Created by liyiguang on 2017/8/18.
// */
//
//class OrgServiceProxyTest extends Specification {
//
//    OrgServiceProxy orgServiceProxy
//
//    def setupSpec() {
//        def i18nClient = Mock(I18nClient)
//        def i18nServiceImpl = Mock(I18nServiceImpl)
//        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
//        // 处理final字段
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
//        i18nClient.getAllLanguage() >> []
//
//        // 模拟RestUtils静态方法
//        GroovyMock(RestUtils, global: true)
//        RestUtils.generateId() >> "mock-id-12345"
//    }
//
//    def setup() {
//        orgServiceProxy = new OrgServiceProxy()
//        orgServiceProxy.socialService = socialService
//    }
//
//    def "测试queryDeptInfoByUserIds方法"() {
//        given:
//        def arg = new QueryDeptInfoByUserIds.Arg(
//                tenantId: "12345",
//                userId: "67890",
//                userIds: ["11111", "22222"]
//        )
//
//        def deptInfo1 = new QueryDeptInfoByUserIds.MainDeptInfo(
//                userId: "11111",
//                deptId: "101",
//                deptName: "研发部"
//        )
//
//        def deptInfo2 = new QueryDeptInfoByUserIds.MainDeptInfo(
//                userId: "22222",
//                deptId: "102",
//                deptName: "产品部"
//        )
//
//        def result = new QueryDeptInfoByUserIds.Result(
//                success: true,
//                result: [deptInfo1, deptInfo2]
//        )
//
//        when:
//        def response = orgServiceProxy.queryDeptInfoByUserIds(arg)
//
//        then:
//        1 * socialService.execute(_, _) >> { args ->
//            String method = args[0]
//
//            assert method == "queryDeptInfoByUserIds"
//            assert request.getTenantId() == arg.tenantId
//            assert request.getUserId() == arg.userId
//            assert request.getParams().get("userIds") == arg.userIds
//
//            ApiResult.createSuccessResult(result)
//        }
//
//        response.success
//        response.result.size() == 2
//        response.result[0].userId == "11111"
//        response.result[0].deptId == "101"
//        response.result[0].deptName == "研发部"
//        response.result[1].userId == "22222"
//        response.result[1].deptId == "102"
//        response.result[1].deptName == "产品部"
//    }
//
//    def "测试queryDeptInfoByUserIds方法 - 失败情况"() {
//        given:
//        def arg = new QueryDeptInfoByUserIds.Arg(
//                tenantId: "12345",
//                userId: "67890",
//                userIds: ["11111", "22222"]
//        )
//
//        when:
//        def response = orgServiceProxy.queryDeptInfoByUserIds(arg)
//
//        then:
//        1 * socialService.execute(_, _) >> ApiResult.createErrorResult("查询失败")
//
//        !response.success
//    }
//
//    def "测试queryGroupByIds方法"() {
//        given:
//        def arg = new QueryGroupByIds.Arg(
//                tenantId: "12345",
//                userId: "67890",
//                groupIdList: ["201", "202"]
//        )
//
//        def groupInfo1 = new QueryGroupByIds.UserGroupInfo(
//                id: "201",
//                name: "项目组1",
//                description: "项目1小组",
//                createTime: System.currentTimeMillis(),
//                memberCount: 5
//        )
//
//        def groupInfo2 = new QueryGroupByIds.UserGroupInfo(
//                id: "202",
//                name: "项目组2",
//                description: "项目2小组",
//                createTime: System.currentTimeMillis(),
//                memberCount: 8
//        )
//
//        def result = new QueryGroupByIds.Result(
//                success: true,
//                result: [groupInfo1, groupInfo2]
//        )
//
//        when:
//        def response = orgServiceProxy.queryGroupByIds(arg)
//
//        then:
//        1 * socialService.execute(_, _) >> { args ->
//            String method = args[0]
//            BaseRequest request = args[1]
//
//            assert method == "queryGroupByIds"
//            assert request.getTenantId() == arg.tenantId
//            assert request.getUserId() == arg.userId
//            assert request.getParams().get("groupIdList") == arg.groupIdList
//
//            ApiResult.createSuccessResult(result)
//        }
//
//        response.success
//        response.result.size() == 2
//        response.result[0].id == "201"
//        response.result[0].name == "项目组1"
//        response.result[0].description == "项目1小组"
//        response.result[0].memberCount == 5
//        response.result[1].id == "202"
//        response.result[1].name == "项目组2"
//    }
//
//    def "测试queryGroupByIds方法 - 失败情况"() {
//        given:
//        def arg = new QueryGroupByIds.Arg(
//                tenantId: "12345",
//                userId: "67890",
//                groupIdList: ["201", "202"]
//        )
//
//        when:
//        def response = orgServiceProxy.queryGroupByIds(arg)
//
//        then:
//        1 * socialService.execute(_, _) >> ApiResult.createErrorResult("查询失败")
//
//        !response.success
//    }
//
//    def "测试queryDeptInfoByDeptIds方法"() {
//        given:
//        def arg = new QueryDeptInfoByDeptIds.Arg(
//                tenantId: "12345",
//                userId: "67890",
//                deptIds: ["101", "102"],
//                status: QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE
//        )
//
//        def deptInfo1 = new QueryDeptInfoByDeptIds.DeptInfo(
//                deptId: "101",
//                deptName: "研发部",
//                managerId: "11111",
//                managerName: "张三",
//                memberCount: 10,
//                status: 0
//        )
//
//        def deptInfo2 = new QueryDeptInfoByDeptIds.DeptInfo(
//                deptId: "102",
//                deptName: "产品部",
//                managerId: "22222",
//                managerName: "李四",
//                memberCount: 5,
//                status: 0
//        )
//
//        def result = new QueryDeptInfoByDeptIds.Result(
//                success: true,
//                result: [deptInfo1, deptInfo2]
//        )
//
//        when:
//        def response = orgServiceProxy.queryDeptInfoByDeptIds(arg)
//
//        then:
//        1 * socialService.execute(_, _) >> { args ->
//            String method = args[0]
//            BaseRequest request = args[1]
//
//            assert method == "queryDeptInfoByDeptIds"
//            assert request.getTenantId() == arg.tenantId
//            assert request.getUserId() == arg.userId
//            assert request.getParams().get("deptIds") == arg.deptIds
//            assert request.getParams().get("status") == arg.status.name()
//
//            ApiResult.createSuccessResult(result)
//        }
//
//        response.success
//        response.result.size() == 2
//        response.result[0].deptId == "101"
//        response.result[0].deptName == "研发部"
//        response.result[0].managerId == "11111"
//        response.result[0].managerName == "张三"
//        response.result[1].deptId == "102"
//        response.result[1].deptName == "产品部"
//    }
//
//    def "测试queryDeptInfoByDeptIds方法 - 失败情况"() {
//        given:
//        def arg = new QueryDeptInfoByDeptIds.Arg(
//                tenantId: "12345",
//                userId: "67890",
//                deptIds: ["101", "102"],
//                status: QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE
//        )
//
//        when:
//        def response = orgServiceProxy.queryDeptInfoByDeptIds(arg)
//
//        then:
//        1 * socialService.execute(_, _) >> ApiResult.createErrorResult("查询失败")
//
//        !response.success
//    }
//
//    def "测试getDeptInfoByName方法"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def names = ["研发部", "产品部"]
//
//        def deptInfo1 = new QueryDeptByName.DeptInfo(
//                id: "101",
//                name: "研发部",
//                managerId: "11111",
//                parentId: "100",
//                ancestors: ["1", "100"],
//                order: 1,
//                deptType: QueryDeptByName.TYPE_DEPT
//        )
//
//        def deptInfo2 = new QueryDeptByName.DeptInfo(
//                id: "102",
//                name: "产品部",
//                managerId: "22222",
//                parentId: "100",
//                ancestors: ["1", "100"],
//                order: 2,
//                deptType: QueryDeptByName.TYPE_DEPT
//        )
//
//        def result = new QueryDeptByName.Result(
//                success: true,
//                result: [deptInfo1, deptInfo2]
//        )
//
//        when:
//        def response = orgServiceProxy.getDeptInfoByName(tenantId, userId, names)
//
//        then:
//        1 * socialService.execute(_, _) >> { args ->
//            String method = args[0]
//            BaseRequest request = args[1]
//
//            assert method == "getDeptInfoByName"
//            assert request.getTenantId() == tenantId
//            assert request.getUserId() == userId
//            assert request.getParams().get("names") == names
//
//            ApiResult.createSuccessResult(result)
//        }
//
//        response.success
//        response.result.size() == 2
//        response.result[0].id == "101"
//        response.result[0].name == "研发部"
//        response.result[0].managerId == "11111"
//        response.result[1].id == "102"
//        response.result[1].name == "产品部"
//    }
//
//    def "测试getDeptInfoByName方法 - 失败情况"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def names = ["研发部", "产品部"]
//
//        when:
//        def response = orgServiceProxy.getDeptInfoByName(tenantId, userId, names)
//
//        then:
//        1 * socialService.execute(_, _) >> ApiResult.createErrorResult("查询失败")
//
//        !response.success
//    }
//
//    def "测试getUserInfoByNickNames方法"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def names = ["张三", "李四"]
//
//        def userInfo1 = new UserInfo(
//                id: "11111",
//                name: "张三",
//                mobile: "13800138001",
//                email: "<EMAIL>",
//                dept: "101",
//                status: 0
//        )
//
//        def userInfo2 = new UserInfo(
//                id: "22222",
//                name: "李四",
//                mobile: "13800138002",
//                email: "<EMAIL>",
//                dept: "102",
//                status: 0
//        )
//
//        def result = ["success": true, "result": [userInfo1, userInfo2]]
//
//        when:
//        def response = orgServiceProxy.getUserInfoByNickNames(tenantId, userId, names, 0)
//
//        then:
//        1 * socialService.execute(_, _) >> { args ->
//            String method = args[0]
//            BaseRequest request = args[1]
//
//            assert method == "getUserInfoByNickNames"
//            assert request.getTenantId() == tenantId
//            assert request.getUserId() == userId
//            assert request.getParams().get("nickNames") == names
//            assert request.getParams().get("status") == 0
//
//            ApiResult.createSuccessResult(result)
//        }
//
//        response.success
//        response.result.size() == 2
//        response.result[0].id == "11111"
//        response.result[0].name == "张三"
//        response.result[1].id == "22222"
//        response.result[1].name == "李四"
//    }
//
//    def "测试getUserInfoByNickNames方法 - 失败情况"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def names = ["张三", "李四"]
//
//        when:
//        def response = orgServiceProxy.getUserInfoByNickNames(tenantId, userId, names, 0)
//
//        then:
//        1 * socialService.execute(_, _) >> ApiResult.createErrorResult("查询失败")
//
//        !response.success
//    }
//
//    def "测试getUserInfoByCodes方法"() {
//        given:
//        def user = new User("12345", "67890")
//        def codes = ["001", "002"]
//
//        def userInfo1 = new UserInfo(
//                id: "11111",
//                name: "张三",
//                mobile: "13800138001",
//                email: "<EMAIL>",
//                dept: "101",
//                status: 0,
//                empNum: "001"
//        )
//
//        def userInfo2 = new UserInfo(
//                id: "22222",
//                name: "李四",
//                mobile: "13800138002",
//                email: "<EMAIL>",
//                dept: "102",
//                status: 0,
//                empNum: "002"
//        )
//
//        def result = ["success": true, "result": [userInfo1, userInfo2]]
//
//        when:
//        def response = orgServiceProxy.getUserInfoByCodes(user, codes)
//
//        then:
//        1 * socialService.execute(_, _) >> { args ->
//            String method = args[0]
//            BaseRequest request = args[1]
//
//            assert method == "getUserInfoByCodes"
//            assert request.getTenantId() == user.tenantId
//            assert request.getUserId() == user.userId
//            assert request.getParams().get("codes") == codes
//
//            ApiResult.createSuccessResult(result)
//        }
//
//        response.success
//        response.result.size() == 2
//        response.result[0].id == "11111"
//        response.result[0].name == "张三"
//        response.result[0].empNum == "001"
//        response.result[1].id == "22222"
//        response.result[1].name == "李四"
//        response.result[1].empNum == "002"
//    }
//
//    def "测试getUserInfoByCodes方法 - 失败情况"() {
//        given:
//        def user = new User("12345", "67890")
//        def codes = ["001", "002"]
//
//        when:
//        def response = orgServiceProxy.getUserInfoByCodes(user, codes)
//
//        then:
//        1 * socialService.execute(_, _) >> ApiResult.createErrorResult("查询失败")
//
//        !response.success
//    }
//
//    def "测试buildParams方法"() {
//        given:
//        def params = [
//                "key1": "value1",
//                "key2": ["item1", "item2"],
//                "key3": 123
//        ]
//
//        when:
//        def request = Whitebox.invokeMethod(orgServiceProxy, "buildParams", "12345", "67890", params)
//
//        then:
//        request.getTenantId() == "12345"
//        request.getUserId() == "67890"
//        request.getParams().get("key1") == "value1"
//        request.getParams().get("key2") == ["item1", "item2"]
//        request.getParams().get("key3") == 123
//    }
//
//    def "测试parseResultData方法 - 成功情况"() {
//        given:
//        def resultData = ["success": true, "result": ["item1", "item2"]]
//
//        when:
//        def parsedResult = Whitebox.invokeMethod(orgServiceProxy, "parseResultData", resultData, "测试")
//
//        then:
//        parsedResult.success
//        parsedResult.result == ["item1", "item2"]
//    }
//
//    def "测试parseResultData方法 - 失败情况"() {
//        given:
//        def resultData = ["success": false, "message": "操作失败"]
//
//        when:
//        def parsedResult = Whitebox.invokeMethod(orgServiceProxy, "parseResultData", resultData, "测试")
//
//        then:
//        !parsedResult.success
//        parsedResult.message == "操作失败"
//    }
//}
