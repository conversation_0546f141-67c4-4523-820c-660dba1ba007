package com.facishare.paas.appframework.common.graph

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：GraphBuilder图构建器的单元测试
 */
class GraphBuilderTest extends Specification {

    def "test directed graph builder"() {
        when: "创建有向图构建器"
        def builder = GraphBuilder.directed()

        then: "应该创建有向图构建器"
        builder != null
    }

    def "test undirected graph builder"() {
        when: "创建无向图构建器"
        def builder = GraphBuilder.undirected()

        then: "应该创建无向图构建器"
        builder != null
    }

    def "test build directed graph"() {
        when: "构建有向图"
        def graph = GraphBuilder.directed().build()

        then: "应该创建有向图"
        graph != null
        graph instanceof MutableGraph
        graph.isDirected()
        !graph.allowsSelfLoops()
        graph.nodeOrder() == ElementOrder.insertion()
    }

    def "test build undirected graph"() {
        when: "构建无向图"
        def graph = GraphBuilder.undirected().build()

        then: "应该创建无向图"
        graph != null
        graph instanceof MutableGraph
        !graph.isDirected()
        !graph.allowsSelfLoops()
        graph.nodeOrder() == ElementOrder.insertion()
    }

    def "test allowsSelfLoops configuration"() {
        when: "配置允许自环"
        def graph = GraphBuilder.directed()
                .allowsSelfLoops(true)
                .build()

        then: "图应该允许自环"
        graph.allowsSelfLoops()

        when: "配置不允许自环"
        def graph2 = GraphBuilder.directed()
                .allowsSelfLoops(false)
                .build()

        then: "图应该不允许自环"
        !graph2.allowsSelfLoops()
    }

    def "test expectedNodeCount configuration"() {
        when: "配置期望节点数"
        def graph = GraphBuilder.directed()
                .expectedNodeCount(100)
                .build()

        then: "应该成功构建图"
        graph != null
        graph.nodes().isEmpty()
    }

    def "test expectedNodeCount with negative value throws exception"() {
        when: "使用负数配置期望节点数"
        GraphBuilder.directed().expectedNodeCount(-1)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test nodeOrder configuration"() {
        when: "配置节点顺序为无序"
        def graph = GraphBuilder.directed()
                .nodeOrder(ElementOrder.unordered())
                .build()

        then: "图应该使用无序节点顺序"
        graph.nodeOrder().type() == ElementOrder.Type.UNORDERED

        when: "配置节点顺序为自然顺序"
        def graph2 = GraphBuilder.directed()
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        then: "图应该使用自然顺序"
        graph2.nodeOrder().type() == ElementOrder.Type.SORTED
    }

    def "test from existing graph"() {
        given: "创建一个现有图"
        def originalGraph = GraphBuilder.directed()
                .allowsSelfLoops(true)
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        when: "从现有图创建构建器"
        def newGraph = GraphBuilder.from(originalGraph).build()

        then: "新图应该继承原图的属性"
        newGraph.isDirected() == originalGraph.isDirected()
        newGraph.allowsSelfLoops() == originalGraph.allowsSelfLoops()
        newGraph.nodeOrder().type() == originalGraph.nodeOrder().type()
    }

    def "test builder method chaining"() {
        when: "链式调用构建器方法"
        def graph = GraphBuilder.directed()
                .allowsSelfLoops(true)
                .expectedNodeCount(50)
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        then: "应该正确应用所有配置"
        graph.isDirected()
        graph.allowsSelfLoops()
        graph.nodeOrder().type() == ElementOrder.Type.SORTED
    }

    def "test multiple builds from same builder"() {
        given: "创建构建器"
        def builder = GraphBuilder.directed().allowsSelfLoops(true)

        when: "多次构建图"
        def graph1 = builder.build()
        def graph2 = builder.build()

        then: "应该创建不同的图实例但具有相同属性"
        graph1 != graph2
        graph1.isDirected() == graph2.isDirected()
        graph1.allowsSelfLoops() == graph2.allowsSelfLoops()
    }

    def "test graph functionality after build"() {
        given: "构建图"
        def graph = GraphBuilder.undirected().build()

        when: "添加节点和边"
        graph.addNode("A")
        graph.addNode("B")
        graph.putEdge("A", "B")

        then: "图应该正确工作"
        graph.nodes().size() == 2
        graph.nodes().contains("A")
        graph.nodes().contains("B")
        graph.hasEdgeConnecting("A", "B")
        graph.hasEdgeConnecting("B", "A") // 无向图
    }

    def "test self loop behavior"() {
        given: "创建允许自环的图"
        def allowSelfLoopGraph = GraphBuilder.directed()
                .allowsSelfLoops(true)
                .build()

        and: "创建不允许自环的图"
        def noSelfLoopGraph = GraphBuilder.directed()
                .allowsSelfLoops(false)
                .build()

        when: "在允许自环的图中添加自环"
        allowSelfLoopGraph.addNode("A")
        allowSelfLoopGraph.putEdge("A", "A")

        then: "应该成功添加自环"
        allowSelfLoopGraph.hasEdgeConnecting("A", "A")

        when: "在不允许自环的图中添加自环"
        noSelfLoopGraph.addNode("B")
        noSelfLoopGraph.putEdge("B", "B")

        then: "应该抛出异常"
        thrown(IllegalArgumentException)
    }

    def "test node order behavior"() {
        given: "创建使用自然顺序的图"
        def naturalOrderGraph = GraphBuilder.directed()
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        when: "以非字母顺序添加节点"
        naturalOrderGraph.addNode("C")
        naturalOrderGraph.addNode("A")
        naturalOrderGraph.addNode("B")

        then: "节点应该按自然顺序排列"
        naturalOrderGraph.nodes() as List == ["A", "B", "C"]
    }

    def "test insertion order behavior"() {
        given: "创建使用插入顺序的图"
        def insertionOrderGraph = GraphBuilder.directed()
                .nodeOrder(ElementOrder.insertion())
                .build()

        when: "添加节点"
        insertionOrderGraph.addNode("C")
        insertionOrderGraph.addNode("A")
        insertionOrderGraph.addNode("B")

        then: "节点应该按插入顺序排列"
        insertionOrderGraph.nodes() as List == ["C", "A", "B"]
    }

    def "test null nodeOrder throws exception"() {
        when: "使用null节点顺序"
        GraphBuilder.directed().nodeOrder(null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test builder immutability"() {
        given: "创建构建器"
        def builder = GraphBuilder.directed()

        when: "配置构建器"
        def modifiedBuilder = builder.allowsSelfLoops(true)

        then: "应该返回相同的构建器实例（流式API）"
        modifiedBuilder.is(builder)
    }

    def "test type safety with generics"() {
        when: "创建字符串类型的图"
        def stringGraph = GraphBuilder.directed().<String>build()

        and: "创建整数类型的图"
        def intGraph = GraphBuilder.directed().<Integer>build()

        then: "类型应该正确"
        stringGraph instanceof MutableGraph<String>
        intGraph instanceof MutableGraph<Integer>
    }

    def "test complex graph construction"() {
        given: "构建复杂图"
        def graph = GraphBuilder.directed()
                .allowsSelfLoops(true)
                .expectedNodeCount(10)
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        when: "添加多个节点和边"
        ["D", "A", "C", "B"].each { graph.addNode(it) }
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")
        graph.putEdge("C", "D")
        graph.putEdge("A", "A") // 自环

        then: "图应该正确构建"
        graph.nodes() as List == ["A", "B", "C", "D"] // 自然顺序
        graph.edges().size() == 4
        graph.hasEdgeConnecting("A", "A") // 自环存在
        graph.successors("A").contains("B")
        graph.successors("A").contains("A")
    }

    def "test edge cases"() {
        expect: "验证边界情况"
        // 零期望节点数
        GraphBuilder.directed().expectedNodeCount(0).build() != null

        // 大期望节点数
        GraphBuilder.directed().expectedNodeCount(1000000).build() != null

        // 多次配置相同属性
        def graph = GraphBuilder.directed()
                .allowsSelfLoops(true)
                .allowsSelfLoops(false)
                .build()
        !graph.allowsSelfLoops() // 最后一次配置生效
    }
}
