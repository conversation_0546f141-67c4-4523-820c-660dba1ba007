package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.MobileInfo
import com.facishare.paas.appframework.core.model.User
import com.facishare.userlogin.api.model.validatecode.BuildValidateCode
import com.facishare.userlogin.api.model.validatecode.VerifyValidateCode
import com.facishare.userlogin.api.service.ValidateCodeService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class SmsCodeServiceImplTest extends Specification {
    
    SmsCodeServiceImpl smsCodeService
    ValidateCodeService validateCodeService = Mock(ValidateCodeService)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        smsCodeService = new SmsCodeServiceImpl()
        smsCodeService.validateCodeService = validateCodeService
    }
    
    def "测试createSmsCode方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        
        def ip = "***********"
        def areaCode = "86"
        def mobile = "***********"
        def captchaCode = "1234"
        def captchaId = "captcha_id_123"
        def expireTime = 180
        
        def expectedResult = new BuildValidateCode.Result()
        expectedResult.result = BuildValidateCode.SendValidateCodeEnum.SUCCESS
        expectedResult.code = "5678"
        
        when:
        def result = smsCodeService.createSmsCode(user, ip, areaCode, mobile, captchaCode, captchaId, expireTime)
        
        then:
        1 * validateCodeService.buildCode(_) >> { args ->
            BuildValidateCode.Argument arg = args[0]
            // 验证手机号格式是否正确处理
            assert arg.key == new MobileInfo(areaCode, mobile).smsMobile()
            assert arg.ip == ip
            assert arg.imgCode == captchaCode
            assert arg.epxId == captchaCode
            assert arg.codeExpireTime == expireTime
            expectedResult
        }
        
        result == expectedResult
    }
    
    def "测试verifySmsCode方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        
        def areaCode = "86"
        def mobile = "***********"
        def smsCode = "1234"
        
        def expectedResult = new VerifyValidateCode.Result()
        expectedResult.result = VerifyValidateCode.VerifyCodeEnum.SUCCESS
        
        when:
        def result = smsCodeService.verifySmsCode(user, areaCode, mobile, smsCode)
        
        then:
        1 * validateCodeService.verifyCode(_) >> { args ->
            VerifyValidateCode.Argument arg = args[0]
            assert arg.key == new MobileInfo(areaCode, mobile).smsMobile()
            assert arg.code == smsCode
            expectedResult
        }
        
        result == "SUCCESS"
    }
    
    def "测试sendSMS方法 - CRM业务"() {
        given:
        def ea = "test_ea"
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        user.getUserIdInt() >> Integer.parseInt(userId)
        
        def areaCode = "86"
        def mobile = "***********"
        def biz = "CRM"
        def content = "验证码: 1234"
        
        def result = new com.facishare.userlogin.api.model.validatecode.SmsMessage.Result()
        result.succeed = true
        
        when:
        def success = smsCodeService.sendSMS(ea, user, areaCode, mobile, biz, content)
        
        then:
        1 * validateCodeService.sendMessage(_) >> { args ->
            com.facishare.userlogin.api.model.validatecode.SmsMessage.Argument arg = args[0]
            assert arg.areaCode == areaCode
            assert arg.mobile == mobile
            assert arg.employeeId == Integer.parseInt(userId)
            assert arg.enterpriseAccount == ea
            assert arg.message == content
            result
        }
        
        success == true
    }
    
    def "测试sendSMS方法 - 非CRM业务"() {
        given:
        def ea = "test_ea"
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        
        def areaCode = "86"
        def mobile = "***********"
        def biz = "LoginDynamicPassword"
        def content = "验证码: 1234"
        
        when:
        def success = smsCodeService.sendSMS(ea, user, areaCode, mobile, biz, content)
        
        then:
        1 * validateCodeService.sendInnerSms(_) >> { args ->
            com.facishare.userlogin.api.model.validatecode.InnerSmsMessage.Argument arg = args[0]
            assert arg.mobile == mobile
            assert arg.content == content
        }
        
        success == true
    }
    
    def "测试sendSmsCode方法 - 成功场景"() {
        given:
        def ea = "test_ea"
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        user.getUserIdInt() >> Integer.parseInt(userId)
        
        def ip = "***********"
        def areaCode = "86"
        def mobile = "***********"
        def captchaCode = "1234"
        def captchaId = "captcha_id_123"
        def biz = "CRM"
        def i18nKey = "default_sm_login_i18n_key"
        
        def buildCodeResult = new BuildValidateCode.Result()
        buildCodeResult.result = BuildValidateCode.SendValidateCodeEnum.SUCCESS
        buildCodeResult.code = "5678"
        
        def sendMessageResult = new com.facishare.userlogin.api.model.validatecode.SmsMessage.Result()
        sendMessageResult.succeed = true
        
        when:
        def result = smsCodeService.sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId, biz, i18nKey)
        
        then:
        1 * validateCodeService.buildCode(_) >> buildCodeResult
        1 * validateCodeService.sendMessage(_) >> sendMessageResult
        
        result == "SUCCESS"
    }
    
    def "测试sendSmsCode方法 - 生成验证码失败"() {
        given:
        def ea = "test_ea"
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        
        def ip = "***********"
        def areaCode = "86"
        def mobile = "***********"
        def captchaCode = "1234"
        def captchaId = "captcha_id_123"
        def biz = "CRM"
        def i18nKey = "default_sm_login_i18n_key"
        
        def buildCodeResult = new BuildValidateCode.Result()
        buildCodeResult.result = BuildValidateCode.SendValidateCodeEnum.REQUEST_TOO_FREQUENT
        
        when:
        def result = smsCodeService.sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId, biz, i18nKey)
        
        then:
        1 * validateCodeService.buildCode(_) >> buildCodeResult
        0 * validateCodeService.sendMessage(_)
        
        result == "REQUEST_TOO_FREQUENT"
    }
    
    def "测试sendSmsCode方法 - 发送短信失败"() {
        given:
        def ea = "test_ea"
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        user.getUserIdInt() >> Integer.parseInt(userId)
        
        def ip = "***********"
        def areaCode = "86"
        def mobile = "***********"
        def captchaCode = "1234"
        def captchaId = "captcha_id_123"
        def biz = "CRM"
        def i18nKey = "default_sm_login_i18n_key"
        
        def buildCodeResult = new BuildValidateCode.Result()
        buildCodeResult.result = BuildValidateCode.SendValidateCodeEnum.SUCCESS
        buildCodeResult.code = "5678"
        
        def sendMessageResult = new com.facishare.userlogin.api.model.validatecode.SmsMessage.Result()
        sendMessageResult.succeed = false
        
        when:
        def result = smsCodeService.sendSmsCode(ea, user, ip, areaCode, mobile, captchaCode, captchaId, biz, i18nKey)
        
        then:
        1 * validateCodeService.buildCode(_) >> buildCodeResult
        1 * validateCodeService.sendMessage(_) >> sendMessageResult
        
        result == "SMS_SEND_ERROR"
    }
} 