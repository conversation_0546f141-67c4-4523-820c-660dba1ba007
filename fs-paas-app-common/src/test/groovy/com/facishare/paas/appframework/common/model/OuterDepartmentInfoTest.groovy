package com.facishare.paas.appframework.common.model

import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class OuterDepartmentInfoTest extends Specification {
    
    def setupSpec() {
        def i18nClient = Mock(com.fxiaoke.i18n.client.I18nClient)
        def i18nServiceImpl = Mock(com.fxiaoke.i18n.client.impl.I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(com.fxiaoke.i18n.client.I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }
    
    def "测试OuterDepartmentInfo构造函数和Getter/Setter方法"() {
        when: "创建OuterDepartmentInfo实例"
        def outerDepartmentInfo = new OuterDepartmentInfo()
        
        then: "默认值都为null"
        outerDepartmentInfo.outerDepartmentId == null
        outerDepartmentInfo.outerDepartmentName == null
        outerDepartmentInfo.outerTenantId == null
        outerDepartmentInfo.outerTenantName == null
        outerDepartmentInfo.status == null
        
        when: "设置属性值"
        outerDepartmentInfo.outerDepartmentId = "dept123"
        outerDepartmentInfo.outerDepartmentName = "测试部门"
        outerDepartmentInfo.outerTenantId = "tenant456"
        outerDepartmentInfo.outerTenantName = "测试租户"
        outerDepartmentInfo.status = 1
        
        then: "getter方法返回正确的值"
        outerDepartmentInfo.outerDepartmentId == "dept123"
        outerDepartmentInfo.outerDepartmentName == "测试部门"
        outerDepartmentInfo.outerTenantId == "tenant456"
        outerDepartmentInfo.outerTenantName == "测试租户"
        outerDepartmentInfo.status == 1
    }
    
    def "测试OuterDepartmentInfo.Builder方法"() {
        when: "使用Builder构建实例"
        def outerDepartmentInfo = OuterDepartmentInfo.builder()
                .outerDepartmentId("dept123")
                .outerDepartmentName("测试部门")
                .outerTenantId("tenant456")
                .outerTenantName("测试租户")
                .status(1)
                .build()
        
        then: "实例包含正确的属性值"
        outerDepartmentInfo.outerDepartmentId == "dept123"
        outerDepartmentInfo.outerDepartmentName == "测试部门"
        outerDepartmentInfo.outerTenantId == "tenant456"
        outerDepartmentInfo.outerTenantName == "测试租户"
        outerDepartmentInfo.status == 1
    }
    
    def "测试OuterDepartmentInfo有参构造函数"() {
        when: "使用全参数构造函数"
        def outerDepartmentInfo = new OuterDepartmentInfo(
                "dept123", "测试部门", "tenant456", "测试租户", 1
        )
        
        then: "实例包含正确的属性值"
        outerDepartmentInfo.outerDepartmentId == "dept123"
        outerDepartmentInfo.outerDepartmentName == "测试部门"
        outerDepartmentInfo.outerTenantId == "tenant456"
        outerDepartmentInfo.outerTenantName == "测试租户"
        outerDepartmentInfo.status == 1
    }
    
    def "测试OuterDepartmentInfo的equals和hashCode方法"() {
        given: "两个相同属性值的OuterDepartmentInfo实例"
        def outerDepartmentInfo1 = new OuterDepartmentInfo(
                "dept123", "测试部门", "tenant456", "测试租户", 1
        )
        def outerDepartmentInfo2 = new OuterDepartmentInfo(
                "dept123", "测试部门", "tenant456", "测试租户", 1
        )
        def outerDepartmentInfo3 = new OuterDepartmentInfo(
                "dept789", "其他部门", "tenant456", "测试租户", 1
        )
        
        expect: "相同属性值的实例equals方法返回true，hashCode相同"
        outerDepartmentInfo1 == outerDepartmentInfo2
        outerDepartmentInfo1.hashCode() == outerDepartmentInfo2.hashCode()
        
        and: "不同属性值的实例equals方法返回false"
        outerDepartmentInfo1 != outerDepartmentInfo3
    }
    
    def "测试OuterDepartmentInfo的toString方法"() {
        given: "一个OuterDepartmentInfo实例"
        def outerDepartmentInfo = new OuterDepartmentInfo(
                "dept123", "测试部门", "tenant456", "测试租户", 1
        )
        
        when: "调用toString方法"
        def result = outerDepartmentInfo.toString()
        
        then: "结果包含所有属性"
        result.contains("outerDepartmentId=dept123")
        result.contains("outerDepartmentName=测试部门")
        result.contains("outerTenantId=tenant456")
        result.contains("outerTenantName=测试租户")
        result.contains("status=1")
    }
    
    def "测试部门状态值"() {
        expect: "正常状态为1，删除状态为2"
        new OuterDepartmentInfo(status: 1).status == 1  // 正常
        new OuterDepartmentInfo(status: 2).status == 2  // 已删除
    }
} 