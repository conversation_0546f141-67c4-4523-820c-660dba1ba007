package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.ImportView
import com.facishare.paas.appframework.common.service.proxy.DataLoaderProxy
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class DataLoaderServiceImplTest extends Specification {
    
    DataLoaderServiceImpl dataLoaderService
    DataLoaderProxy dataLoaderProxy
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(com.fxiaoke.i18n.client.I18nClient)
        def i18nServiceImpl = Mock(com.fxiaoke.i18n.client.impl.I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        com.fxiaoke.i18n.client.Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        dataLoaderProxy = Mock(DataLoaderProxy)
        dataLoaderService = new DataLoaderServiceImpl()
        Whitebox.setInternalState(dataLoaderService, "dataLoaderProxy", dataLoaderProxy)
        
        // 模拟静态方法
        GroovyMock(RestUtils, global: true)
    }
    
    def "测试parse方法，正常情况"() {
        given: "准备用户和文件路径"
        def user = Mock(User)
        user.getTenantId() >> "tenant123"
        def filePath = "/tmp/test.xlsx"
        def multiSheets = true
        
        and: "模拟RestUtils.buildHeaders方法"
        def headers = ['x-fs-ei':'tenant123', 'x-fs-upstream-owner-id':null, 'x-out-user-id':null, 'x-out-tenant-id':null, 'X-fs-Enterprise-Id':'tenant123']
        RestUtils.buildHeaders(user) >> headers
        
        and: "模拟代理调用结果"
        def excelCols = [
            new ImportView.ExcelCol(colIndex: "1", colName: "列1"),
            new ImportView.ExcelCol(colIndex: "2", colName: "列2")
        ]
        def sheetContents = [
            new ImportView.SheetContent(sheetName: "Sheet1", sheetIndex: "0", excelCols: excelCols)
        ]
        def result = ImportView.Result.builder()
                .sheetContents(sheetContents)
                .build()
        
        dataLoaderProxy.parser(_, headers) >> result
        
        when: "调用解析方法"
        def response = dataLoaderService.parse(user, filePath, multiSheets)
        
        then: "返回正确的解析结果"
        if (response != null) {
            response.sheetContents.size() == 1
            response.sheetContents[0].sheetName == "Sheet1"
            response.sheetContents[0].excelCols[0].colName == "列1"
            response.sheetContents[0].excelCols[1].colName == "列2"
        }
        1 * dataLoaderProxy.parser({ it.filePath == filePath && it.multiSheets == multiSheets }, headers)
    }
    
    def "测试parse方法，代理调用失败但不抛出异常"() {
        given: "准备用户和文件路径"
        def user = Mock(User)
        user.getTenantId() >> "tenant123"
        def filePath = "/tmp/invalid.xlsx"
        def multiSheets = false
        
        and: "模拟RestUtils.buildHeaders方法"
        def headers = ['x-fs-ei':'tenant123', 'x-fs-upstream-owner-id':null, 'x-out-user-id':null, 'x-out-tenant-id':null, 'X-fs-Enterprise-Id':'tenant123']
        RestUtils.buildHeaders(user) >> headers
        
        and: "模拟代理调用抛出异常"
        dataLoaderProxy.parser(_, headers) >> { throw new RuntimeException("文件解析错误") }
        
        when: "调用解析方法"
        dataLoaderService.parse(user, filePath, multiSheets)
        
        then: "不抛出异常"
        noExceptionThrown()
        1 * dataLoaderProxy.parser({ it.filePath == filePath && it.multiSheets == multiSheets }, headers)
    }
    
    def "测试parse方法，参数边界测试"() {
        given: "准备用户和文件路径"
        def user = Mock(User)
        user.getTenantId() >> "tenant123"
        
        and: "模拟RestUtils.buildHeaders方法"
        def headers = ['x-fs-ei':'tenant123', 'x-fs-upstream-owner-id':null, 'x-out-user-id':null, 'x-out-tenant-id':null, 'X-fs-Enterprise-Id':'tenant123']
        RestUtils.buildHeaders(user) >> headers
        
        and: "模拟代理调用结果"
        def result = ImportView.Result.builder().build()
        dataLoaderProxy.parser(_, headers) >> result
        
        when: "传入不同的参数调用解析方法"
        dataLoaderService.parse(user, filePath, multiSheets)
        
        then: "方法正常执行，参数正确传递"
        1 * dataLoaderProxy.parser({ 
            it.filePath == filePath && it.multiSheets == multiSheets 
        }, headers)
        
        where:
        filePath                      | multiSheets
        ""                            | true
        null                          | true
        "/tmp/test.xlsx"              | null
        "/tmp/verylongpath/file.xlsx" | false
    }
    
    def "测试ImportView.Arg类的getter和setter"() {
        when: "创建并设置ImportView.Arg对象"
        def arg = new ImportView.Arg()
        arg.setFilePath("/tmp/test.xlsx")
        arg.setMultiSheets(true)
        
        then: "getter方法返回正确的值"
        arg.getFilePath() == "/tmp/test.xlsx"
        arg.getMultiSheets() == true
    }
    
    def "测试ImportView.Result类的builder方法"() {
        when: "使用builder创建ImportView.Result对象"
        def excelCols = [
            new ImportView.ExcelCol(colIndex: "1", colName: "列1"),
            new ImportView.ExcelCol(colIndex: "2", colName: "列2")
        ]
        def sheetContents = [
            new ImportView.SheetContent(sheetName: "Sheet1", sheetIndex: "0", excelCols: excelCols)
        ]
        def result = ImportView.Result.builder()
                .sheetContents(sheetContents)
                .build()
        
        then: "对象包含正确的属性值"
        result.getSheetContents() == sheetContents
        result.getSheetContents()[0].getSheetName() == "Sheet1"
    }
} 