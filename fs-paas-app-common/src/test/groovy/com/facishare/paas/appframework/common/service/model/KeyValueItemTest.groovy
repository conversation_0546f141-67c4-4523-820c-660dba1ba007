package com.facishare.paas.appframework.common.service.model

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：KeyValueItem类的单元测试
 */
class KeyValueItemTest extends Specification {

    def "test KeyValueItem default constructor"() {
        when: "创建KeyValueItem实例"
        def keyValueItem = new KeyValueItem()

        then: "默认值应该为null"
        keyValueItem.keyElement == null
        keyValueItem.valueElement == null
    }

    def "test KeyValueItem getter and setter methods"() {
        given: "创建KeyValueItem实例"
        def keyValueItem = new KeyValueItem()
        def keyElement = new KeyValueItem.TextElement()
        def valueElement = new KeyValueItem.TextElement()

        when: "设置keyElement和valueElement"
        keyValueItem.setKeyElement(keyElement)
        keyValueItem.setValueElement(valueElement)

        then: "getter方法应该返回正确的值"
        keyValueItem.getKeyElement() == keyElement
        keyValueItem.getValueElement() == valueElement
    }

    def "test KeyValueItem with null elements"() {
        given: "创建KeyValueItem实例"
        def keyValueItem = new KeyValueItem()

        when: "设置null值"
        keyValueItem.setKeyElement(null)
        keyValueItem.setValueElement(null)

        then: "应该能够设置null值"
        keyValueItem.getKeyElement() == null
        keyValueItem.getValueElement() == null
    }

    def "test TextElement default constructor"() {
        when: "创建TextElement实例"
        def textElement = new KeyValueItem.TextElement()

        then: "默认值应该为null"
        textElement.text == null
    }

    def "test TextElement getter and setter methods"() {
        given: "创建TextElement实例"
        def textElement = new KeyValueItem.TextElement()

        when: "设置text值"
        textElement.setText("test text")

        then: "getter方法应该返回正确的值"
        textElement.getText() == "test text"
    }

    def "test TextElement with null text"() {
        given: "创建TextElement实例"
        def textElement = new KeyValueItem.TextElement()

        when: "设置null text"
        textElement.setText(null)

        then: "应该能够设置null值"
        textElement.getText() == null
    }

    def "test TextElement with empty text"() {
        given: "创建TextElement实例"
        def textElement = new KeyValueItem.TextElement()

        when: "设置空字符串"
        textElement.setText("")

        then: "应该能够设置空字符串"
        textElement.getText() == ""
    }

    def "test complete KeyValueItem with TextElements"() {
        given: "创建完整的KeyValueItem"
        def keyValueItem = new KeyValueItem()
        def keyElement = new KeyValueItem.TextElement()
        def valueElement = new KeyValueItem.TextElement()

        when: "设置完整的数据"
        keyElement.setText("key text")
        valueElement.setText("value text")
        keyValueItem.setKeyElement(keyElement)
        keyValueItem.setValueElement(valueElement)

        then: "所有数据应该正确设置"
        keyValueItem.getKeyElement().getText() == "key text"
        keyValueItem.getValueElement().getText() == "value text"
    }

    def "test KeyValueItem equals and hashCode"() {
        given: "创建两个相同的KeyValueItem实例"
        def keyElement1 = new KeyValueItem.TextElement()
        keyElement1.setText("key")
        def valueElement1 = new KeyValueItem.TextElement()
        valueElement1.setText("value")
        def keyValueItem1 = new KeyValueItem()
        keyValueItem1.setKeyElement(keyElement1)
        keyValueItem1.setValueElement(valueElement1)

        def keyElement2 = new KeyValueItem.TextElement()
        keyElement2.setText("key")
        def valueElement2 = new KeyValueItem.TextElement()
        valueElement2.setText("value")
        def keyValueItem2 = new KeyValueItem()
        keyValueItem2.setKeyElement(keyElement2)
        keyValueItem2.setValueElement(valueElement2)

        when: "比较两个实例"
        def areEqual = keyValueItem1.equals(keyValueItem2)
        def hashCodesEqual = keyValueItem1.hashCode() == keyValueItem2.hashCode()

        then: "应该相等且hashCode相同"
        areEqual == true
        hashCodesEqual == true
    }

    def "test TextElement equals and hashCode"() {
        given: "创建两个相同的TextElement实例"
        def textElement1 = new KeyValueItem.TextElement()
        textElement1.setText("test")
        def textElement2 = new KeyValueItem.TextElement()
        textElement2.setText("test")

        when: "比较两个实例"
        def areEqual = textElement1.equals(textElement2)
        def hashCodesEqual = textElement1.hashCode() == textElement2.hashCode()

        then: "应该相等且hashCode相同"
        areEqual == true
        hashCodesEqual == true
    }

    def "test KeyValueItem toString method"() {
        given: "创建KeyValueItem实例并设置数据"
        def keyElement = new KeyValueItem.TextElement()
        keyElement.setText("testKey")
        def valueElement = new KeyValueItem.TextElement()
        valueElement.setText("testValue")
        def keyValueItem = new KeyValueItem()
        keyValueItem.setKeyElement(keyElement)
        keyValueItem.setValueElement(valueElement)

        when: "调用toString方法"
        def toStringResult = keyValueItem.toString()

        then: "toString结果应该包含相关信息"
        toStringResult != null
        toStringResult.length() > 0
    }

    def "test TextElement toString method"() {
        given: "创建TextElement实例并设置数据"
        def textElement = new KeyValueItem.TextElement()
        textElement.setText("test text")

        when: "调用toString方法"
        def toStringResult = textElement.toString()

        then: "toString结果应该包含text信息"
        toStringResult != null
        toStringResult.contains("test text")
    }

    def "test KeyValueItem with different TextElement instances"() {
        given: "创建不同的TextElement实例"
        def keyElement1 = new KeyValueItem.TextElement()
        keyElement1.setText("key1")
        def keyElement2 = new KeyValueItem.TextElement()
        keyElement2.setText("key2")

        def keyValueItem = new KeyValueItem()

        when: "先设置一个keyElement，再设置另一个"
        keyValueItem.setKeyElement(keyElement1)
        def firstKey = keyValueItem.getKeyElement().getText()
        
        keyValueItem.setKeyElement(keyElement2)
        def secondKey = keyValueItem.getKeyElement().getText()

        then: "应该正确更新"
        firstKey == "key1"
        secondKey == "key2"
    }
}
