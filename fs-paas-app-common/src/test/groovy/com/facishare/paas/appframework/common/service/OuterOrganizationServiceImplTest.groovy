//package com.facishare.paas.appframework.common.service
//
//import com.facishare.organization.adapter.api.model.organizationwithouter.BatchGetEmployee
//import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee
//import com.facishare.organization.adapter.api.service.OrganizationWithOuterService
//import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds
//import com.facishare.paas.appframework.core.model.User
//import com.fxiaoke.enterpriserelation2.data.ErDepartmentSimpleData
//import com.fxiaoke.i18n.client.I18nClient
//import com.fxiaoke.i18n.client.impl.I18nServiceImpl
//import org.apache.commons.lang3.StringUtils

//import org.powermock.reflect.Whitebox
//import spock.lang.Specification
//
//class OuterOrganizationServiceImplTest extends Specification {
//
//    OuterOrganizationServiceImpl outerOrganizationService
//    OrganizationWithOuterService organizationWithOuterService = Mock(OrganizationWithOuterService)
//
//    def setupSpec() {
//        def i18nClient = Mock(I18nClient)
//        def i18nServiceImpl = Mock(I18nServiceImpl)
//        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
//        // 处理final字段
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

//        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
//        i18nClient.getAllLanguage() >> []
//    }
//
//    def setup() {
//        outerOrganizationService = new OuterOrganizationServiceImpl()
//        outerOrganizationService.organizationWithOuterService = organizationWithOuterService
//    }
//
//    def "测试batchGetEmployee方法"() {
//        given:
//        def tenantId = "12345"
//        def outerUserIds = ["111111111111", "222222222222"] as Set
//
//        def employee1 = new OrganizationEmployee(
//                employeeId: "111111111111",
//                name: "外部用户1",
//                enterpriseId: "98765",
//                mainDepartmentId: "101",
//                status: "0"
//        )
//
//        def employee2 = new OrganizationEmployee(
//                employeeId: "222222222222",
//                name: "外部用户2",
//                enterpriseId: "98765",
//                mainDepartmentId: "102",
//                status: "0"
//        )
//
//
//        when:
//        def employees = outerOrganizationService.batchGetEmployee(tenantId, outerUserIds)
//
//        then:
//        1 * organizationWithOuterService.batchGetEmployee(_) >> { args ->
//            BatchGetEmployee arg = args[0 as String] as BatchGetEmployee
//        }
//
//        employees.size() == 2
//        employees[0].employeeId == "111111111111"
//        employees[0].name == "外部用户1"
//        employees[1].employeeId == "222222222222"
//        employees[1].name == "外部用户2"
//    }
//
//    def "测试batchGetEmployee方法 - 返回空"() {
//        given:
//        def tenantId = "12345"
//        def outerUserIds = ["111111111111", "222222222222"] as Set
//
//        def result = new BatchGetOrganizationEmployeeResult(
//                success: true,
//                code: 0,
//                message: "success",
//                data: []
//        )
//
//        when:
//        def employees = outerOrganizationService.batchGetEmployee(tenantId, outerUserIds)
//
//        then:
//        1 * outerAdapterClient.batchGetOrganizationEmployee(_) >> result
//
//        employees.isEmpty()
//    }
//
//    def "测试batchGetEmployee方法 - 结果为null"() {
//        given:
//        def tenantId = "12345"
//        def outerUserIds = ["111111111111", "222222222222"] as Set
//
//        def result = new BatchGetOrganizationEmployeeResult(
//                success: true,
//                code: 0,
//                message: "success",
//                data: null
//        )
//
//        when:
//        def employees = outerOrganizationService.batchGetEmployee(tenantId, outerUserIds)
//
//        then:
//        1 * outerAdapterClient.batchGetOrganizationEmployee(_) >> result
//
//        employees.isEmpty()
//    }
//
//    def "测试batchGetEmployee方法 - 单个用户"() {
//        given:
//        def tenantId = "12345"
//        def outerUserId = "111111111111"
//
//        def employee = new OrganizationEmployee(
//                employeeId: outerUserId,
//                name: "外部用户1",
//                enterpriseId: "98765",
//                mainDepartmentId: "101",
//                status: "0"
//        )
//
//        def result = new BatchGetOrganizationEmployeeResult(
//                success: true,
//                code: 0,
//                message: "success",
//                data: [employee]
//        )
//
//        when:
//        def employees = outerOrganizationService.batchGetEmployee(tenantId, [outerUserId])
//
//        then:
//        1 * outerAdapterClient.batchGetOrganizationEmployee(_) >> result
//
//        employees.size() == 1
//        employees[0].employeeId == outerUserId
//        employees[0].name == "外部用户1"
//    }
//
//    def "测试batchGetOutDepartment方法"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def user = new User(tenantId, userId)
//        def deptIds = ["101", "102"]
//        def status = QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE
//
//        def dept1 = new ErDepartmentSimpleData(
//                id: "101",
//                name: "外部部门1",
//                pid: "100",
//                status: 1
//        )
//
//        def dept2 = new ErDepartmentSimpleData(
//                id: "102",
//                name: "外部部门2",
//                pid: "100",
//                status: 0
//        )
//
//        def arg = new BatchGetDeptArg(
//                tenantId: tenantId,
//                operatorId: userId,
//                deptIds: deptIds,
//                fetchDisable: status == QueryDeptInfoByDeptIds.DeptStatusEnum.ALL
//        )
//
//        def batchResult = new BatchGetDeptResult(
//                code: 0,
//                message: "success",
//                success: true,
//                data: [dept1, dept2]
//        )
//
//        when:
//        def result = outerOrganizationService.batchGetOutDepartment(user, deptIds, status)
//
//        then:
//        1 * enterpriseRelation2Client.batchGetDept(_) >> { args ->
//            BatchGetDeptArg batchArg = args[0]
//            assert batchArg.tenantId == tenantId
//            assert batchArg.operatorId == userId
//            assert batchArg.deptIds == deptIds
//            assert batchArg.fetchDisable == (status == QueryDeptInfoByDeptIds.DeptStatusEnum.ALL)
//            batchResult
//        }
//
//        result.size() == 2
//        result[0].id == "101"
//        result[0].name == "外部部门1"
//        result[1].id == "102"
//        result[1].name == "外部部门2"
//    }
//
//    def "测试batchGetOutDepartment方法 - 返回空"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def user = new User(tenantId, userId)
//        def deptIds = ["101", "102"]
//        def status = QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE
//
//        def batchResult = new BatchGetDeptResult(
//                code: 0,
//                message: "success",
//                success: true,
//                data: []
//        )
//
//        when:
//        def result = outerOrganizationService.batchGetOutDepartment(user, deptIds, status)
//
//        then:
//        1 * enterpriseRelation2Client.batchGetDept(_) >> batchResult
//
//        result.isEmpty()
//    }
//
//    def "测试batchGetOutDepartment方法 - 返回null"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def user = new User(tenantId, userId)
//        def deptIds = ["101", "102"]
//        def status = QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE
//
//        def batchResult = new BatchGetDeptResult(
//                code: 0,
//                message: "success",
//                success: true,
//                data: null
//        )
//
//        when:
//        def result = outerOrganizationService.batchGetOutDepartment(user, deptIds, status)
//
//        then:
//        1 * enterpriseRelation2Client.batchGetDept(_) >> batchResult
//
//        result.isEmpty()
//    }
//
//    def "测试getOutEmployeeRelation方法"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def employeeIds = ["111111111111", "222222222222"]
//
//        def relation1 = new ErEmployeeRelationSimpleData(
//                id: "111111111111",
//                name: "外部用户1",
//                mainDeptId: "101",
//                mainDeptName: "研发部",
//                status: 0
//        )
//
//        def relation2 = new ErEmployeeRelationSimpleData(
//                id: "222222222222",
//                name: "外部用户2",
//                mainDeptId: "102",
//                mainDeptName: "产品部",
//                status: 0
//        )
//
//        def arg = new BatchGetEmployeeRelationArg(
//                tenantId: tenantId,
//                operatorId: userId,
//                employeeIds: employeeIds
//        )
//
//        def result = new BatchGetEmployeeRelationResult(
//                code: 0,
//                message: "success",
//                success: true,
//                data: [relation1, relation2]
//        )
//
//        when:
//        def relations = outerOrganizationService.getOutEmployeeRelation(tenantId, userId, employeeIds)
//
//        then:
//        1 * enterpriseRelation2Client.batchGetEmployeeRelation(_) >> { args ->
//            BatchGetEmployeeRelationArg batchArg = args[0]
//            assert batchArg.tenantId == tenantId
//            assert batchArg.operatorId == userId
//            assert batchArg.employeeIds == employeeIds
//            result
//        }
//
//        relations.size() == 2
//        relations[0].id == "111111111111"
//        relations[0].name == "外部用户1"
//        relations[0].mainDeptId == "101"
//        relations[0].mainDeptName == "研发部"
//        relations[1].id == "222222222222"
//        relations[1].name == "外部用户2"
//    }
//
//    def "测试getOutEmployeeRelation方法 - 返回空"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def employeeIds = ["111111111111", "222222222222"]
//
//        def result = new BatchGetEmployeeRelationResult(
//                code: 0,
//                message: "success",
//                success: true,
//                data: []
//        )
//
//        when:
//        def relations = outerOrganizationService.getOutEmployeeRelation(tenantId, userId, employeeIds)
//
//        then:
//        1 * enterpriseRelation2Client.batchGetEmployeeRelation(_) >> result
//
//        relations.isEmpty()
//    }
//
//    def "测试getOutEmployeeRelation方法 - 返回null"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def employeeIds = ["111111111111", "222222222222"]
//
//        def result = new BatchGetEmployeeRelationResult(
//                code: 0,
//                message: "success",
//                success: true,
//                data: null
//        )
//
//        when:
//        def relations = outerOrganizationService.getOutEmployeeRelation(tenantId, userId, employeeIds)
//
//        then:
//        1 * enterpriseRelation2Client.batchGetEmployeeRelation(_) >> result
//
//        relations.isEmpty()
//    }
//
//    def "测试getOrgDetail方法"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def operatorId = "11111"
//
//        def detail = new OrganizationWithOuterQueryDetail(
//                name: "测试企业",
//                logo: "http://example.com/logo.jpg",
//                adminName: "张三",
//                adminMobile: "13800138000"
//        )
//
//        def result = new OrganizationWithOuterResult(
//                success: true,
//                code: 0,
//                message: "success",
//                data: detail
//        )
//
//        when:
//        def orgDetail = outerOrganizationService.getOrgDetail(tenantId, userId, operatorId)
//
//        then:
//        1 * outerAdapterClient.getOrganizationDetail(_) >> { args ->
//            assert args[0].param.organizationId == tenantId
//            assert args[0].param.employeeId == userId
//            assert args[0].param.operatorId == operatorId
//            result
//        }
//
//        orgDetail.name == "测试企业"
//        orgDetail.logo == "http://example.com/logo.jpg"
//        orgDetail.adminName == "张三"
//        orgDetail.adminMobile == "13800138000"
//    }
//
//    def "测试getOrgDetail方法 - 返回null"() {
//        given:
//        def tenantId = "12345"
//        def userId = "67890"
//        def operatorId = "11111"
//
//        def result = new OrganizationWithOuterResult(
//                success: true,
//                code: 0,
//                message: "success",
//                data: null
//        )
//
//        when:
//        def orgDetail = outerOrganizationService.getOrgDetail(tenantId, userId, operatorId)
//
//        then:
//        1 * outerAdapterClient.getOrganizationDetail(_) >> result
//
//        orgDetail == null
//    }
//}