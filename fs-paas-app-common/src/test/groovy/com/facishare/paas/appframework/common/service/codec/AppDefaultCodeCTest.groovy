package com.facishare.paas.appframework.common.service.codec

import com.facishare.rest.core.exception.RestProxyRuntimeException
import com.facishare.rest.core.util.JsonUtil
import org.apache.http.HttpStatus
import spock.lang.Specification
import spock.lang.Unroll

import static java.nio.charset.StandardCharsets.UTF_8

/**
 * GenerateByAI
 * 测试内容描述：AppDefaultCodeC编解码器的单元测试
 */
class AppDefaultCodeCTest extends Specification {

    def appDefaultCodeC = new AppDefaultCodeC()

    def "test encodeArg with null object"() {
        when: "编码null对象"
        def result = appDefaultCodeC.encodeArg(null)

        then: "应该返回null"
        result == null
    }

    def "test encodeArg with string object"() {
        given: "字符串对象"
        def testString = "test string"

        when: "编码字符串对象"
        def result = appDefaultCodeC.encodeArg(testString)

        then: "应该返回UTF-8编码的字节数组"
        result == testString.getBytes(UTF_8)
        new String(result, UTF_8) == testString
    }

    def "test encodeArg with non-string object"() {
        given: "非字符串对象"
        def testObject = [name: "test", value: 123]

        when: "编码非字符串对象"
        def result = appDefaultCodeC.encodeArg(testObject)

        then: "应该返回JSON编码的字节数组"
        result != null
        def jsonString = new String(result, UTF_8)
        jsonString.contains("test")
        jsonString.contains("123")
    }

    def "test encodeArg with empty string"() {
        given: "空字符串"
        def emptyString = ""

        when: "编码空字符串"
        def result = appDefaultCodeC.encodeArg(emptyString)

        then: "应该返回空字节数组"
        result == emptyString.getBytes(UTF_8)
        result.length == 0
    }

    @Unroll
    def "test decodeResult with successful status code #statusCode"() {
        given: "成功的状态码和响应数据"
        def responseBody = "test response"
        def bytes = responseBody.getBytes(UTF_8)
        def headers = [:]

        when: "解码响应"
        def result = appDefaultCodeC.decodeResult(statusCode, headers, bytes, String.class)

        then: "应该返回响应体字符串"
        result == responseBody

        where:
        statusCode << [200, 201, 204, 299]
    }

    @Unroll
    def "test decodeResult with error status code #statusCode throws exception"() {
        given: "错误的状态码"
        def responseBody = "error response"
        def bytes = responseBody.getBytes(UTF_8)
        def headers = [:]

        when: "解码响应"
        appDefaultCodeC.decodeResult(statusCode, headers, bytes, String.class)

        then: "应该抛出RestProxyRuntimeException"
        def exception = thrown(RestProxyRuntimeException)
        exception.message.contains(responseBody)

        where:
        statusCode << [300, 400, 401, 404, 500, 502]
    }

    def "test decodeResult with String class"() {
        given: "字符串响应"
        def responseBody = "string response"
        def bytes = responseBody.getBytes(UTF_8)
        def headers = [:]

        when: "解码为String类型"
        def result = appDefaultCodeC.decodeResult(200, headers, bytes, String.class)

        then: "应该直接返回字符串"
        result == responseBody
        result instanceof String
    }

    def "test decodeResult with JSON object"() {
        given: "JSON响应"
        def testObject = [name: "test", value: 123]
        def jsonString = JsonUtil.toJson(testObject)
        def bytes = jsonString.getBytes(UTF_8)
        def headers = [:]

        when: "解码为Map类型"
        def result = appDefaultCodeC.decodeResult(200, headers, bytes, Map.class)

        then: "应该返回解析后的对象"
        result != null
        result instanceof Map
        result.name == "test"
        result.value == 123
    }

    def "test decodeResult with empty response body"() {
        given: "空响应体"
        def bytes = "".getBytes(UTF_8)
        def headers = [:]

        when: "解码空响应"
        def result = appDefaultCodeC.decodeResult(200, headers, bytes, String.class)

        then: "应该返回空字符串"
        result == ""
    }

    def "test decodeResult with null bytes"() {
        given: "null字节数组"
        def headers = [:]

        when: "解码null字节数组"
        def result = appDefaultCodeC.decodeResult(200, headers, null, String.class)

        then: "应该抛出异常"
        thrown(Exception)
    }

    def "test decodeResult boundary status codes"() {
        given: "边界状态码"
        def responseBody = "boundary test"
        def bytes = responseBody.getBytes(UTF_8)
        def headers = [:]

        when: "测试边界状态码299（成功）"
        def result1 = appDefaultCodeC.decodeResult(299, headers, bytes, String.class)

        then: "应该成功"
        result1 == responseBody

        when: "测试边界状态码300（失败）"
        appDefaultCodeC.decodeResult(300, headers, bytes, String.class)

        then: "应该抛出异常"
        thrown(RestProxyRuntimeException)
    }
}
