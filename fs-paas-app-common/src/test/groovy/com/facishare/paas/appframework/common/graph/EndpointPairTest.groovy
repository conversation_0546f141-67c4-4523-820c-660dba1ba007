package com.facishare.paas.appframework.common.graph

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：EndpointPair类的单元测试
 */
class EndpointPairTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair.ordered静态工厂方法创建有序对
     */
    def "测试EndpointPair.ordered创建有序对"() {
        when: "创建有序的端点对"
        def pair = EndpointPair.ordered("source", "target")
        
        then: "应返回一个Ordered实例"
        pair.isOrdered()
        pair.source() == "source"
        pair.target() == "target"
        pair.nodeU() == "source"
        pair.nodeV() == "target"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair.unordered静态工厂方法创建无序对
     */
    def "测试EndpointPair.unordered创建无序对"() {
        when: "创建无序的端点对"
        def pair = EndpointPair.unordered("nodeU", "nodeV")
        
        then: "应返回一个Unordered实例"
        !pair.isOrdered()
        pair.nodeU() != null
        pair.nodeV() != null
        
        and: "无序对上调用source()应抛出UnsupportedOperationException"
        when: "调用source()"
        pair.source()
        
        then: "应抛出异常"
        thrown(UnsupportedOperationException)
        
        and: "无序对上调用target()应抛出UnsupportedOperationException"
        when: "调用target()"
        pair.target()
        
        then: "应抛出异常"
        thrown(UnsupportedOperationException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair.adjacentNode方法
     */
    def "测试adjacentNode方法"() {
        given: "创建有序和无序的端点对"
        def orderedPair = EndpointPair.ordered("source", "target")
        def unorderedPair = EndpointPair.unordered("nodeU", "nodeV")
        
        expect: "有序端点对的adjacentNode应返回相邻节点"
        orderedPair.adjacentNode("source") == "target"
        orderedPair.adjacentNode("target") == "source"
        
        and: "无序端点对的adjacentNode应返回相邻节点"
        unorderedPair.adjacentNode(unorderedPair.nodeU()) == unorderedPair.nodeV()
        unorderedPair.adjacentNode(unorderedPair.nodeV()) == unorderedPair.nodeU()
        
        when: "传入不存在的节点"
        orderedPair.adjacentNode("nonexistent")
        
        then: "应抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair的迭代功能
     */
    def "测试EndpointPair的迭代功能"() {
        given: "创建有序和无序的端点对"
        def orderedPair = EndpointPair.ordered("source", "target")
        def unorderedPair = EndpointPair.unordered("nodeU", "nodeV")
        
        when: "将有序端点对转换为列表"
        def orderedList = []
        orderedPair.each { orderedList << it }
        
        then: "列表应包含nodeU和nodeV，顺序一致"
        orderedList == ["source", "target"]
        
        when: "将无序端点对转换为列表"
        def unorderedList = []
        unorderedPair.each { unorderedList << it }
        
        then: "列表应包含nodeU和nodeV，顺序一致"
        unorderedList == [unorderedPair.nodeU(), unorderedPair.nodeV()]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair的equals方法
     */
    @Unroll
    def "测试equals方法：pair1=#pair1, pair2=#pair2, 预期结果=#expectedResult"() {
        expect: "equals方法应返回预期结果"
        (pair1.equals(pair2)) == expectedResult
        
        where:
        pair1                                | pair2                                | expectedResult
        EndpointPair.ordered("A", "B")       | EndpointPair.ordered("A", "B")       | true
        EndpointPair.ordered("A", "B")       | EndpointPair.ordered("B", "A")       | false
        EndpointPair.ordered("A", "B")       | EndpointPair.unordered("A", "B")     | false
        EndpointPair.unordered("A", "B")     | EndpointPair.unordered("A", "B")     | true
        EndpointPair.unordered("A", "B")     | EndpointPair.unordered("B", "A")     | true
        EndpointPair.unordered("A", "B")     | EndpointPair.ordered("A", "B")       | false
        EndpointPair.ordered("A", "A")       | EndpointPair.ordered("A", "A")       | true
        EndpointPair.unordered("A", "A")     | EndpointPair.unordered("A", "A")     | true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair的hashCode方法
     */
    def "测试hashCode方法"() {
        expect: "相等的端点对应具有相同的hashCode"
        EndpointPair.ordered("A", "B").hashCode() == EndpointPair.ordered("A", "B").hashCode()
        EndpointPair.unordered("A", "B").hashCode() == EndpointPair.unordered("A", "B").hashCode()
        EndpointPair.unordered("A", "B").hashCode() == EndpointPair.unordered("B", "A").hashCode()
        
        and: "不相等的端点对通常具有不同的hashCode"
        EndpointPair.ordered("A", "B").hashCode() != EndpointPair.ordered("B", "A").hashCode()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair的toString方法
     */
    def "测试toString方法"() {
        given: "创建有序和无序的端点对"
        def orderedPair = EndpointPair.ordered("source", "target")
        def unorderedPair = EndpointPair.unordered("nodeU", "nodeV")
        
        when: "调用toString方法"
        def orderedString = orderedPair.toString()
        def unorderedString = unorderedPair.toString()
        
        then: "有序对的字符串表示应包含方向箭头"
        orderedString.contains("source")
        orderedString.contains("target")
        orderedString.contains("->")
        
        and: "无序对的字符串表示应包含无序连接符"
        unorderedString.contains(unorderedPair.nodeU().toString())
        unorderedString.contains(unorderedPair.nodeV().toString())
        unorderedString.contains("~")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试EndpointPair.of静态工厂方法
     */
    def "测试EndpointPair.of方法"() {
        given: "创建有向图和无向图的模拟对象"
        def directedGraph = Mock(Graph)
        def undirectedGraph = Mock(Graph)
        
        and: "配置模拟对象的行为"
        directedGraph.isDirected() >> true
        undirectedGraph.isDirected() >> false
        
        when: "为有向图创建端点对"
        def directedPair = EndpointPair.of(directedGraph, "A", "B")
        
        then: "应返回有序端点对"
        directedPair.isOrdered()
        directedPair.source() == "A"
        directedPair.target() == "B"
        
        when: "为无向图创建端点对"
        def undirectedPair = EndpointPair.of(undirectedGraph, "A", "B")
        
        then: "应返回无序端点对"
        !undirectedPair.isOrdered()
        undirectedPair.nodeU() in ["A", "B"]
        undirectedPair.nodeV() in ["A", "B"]
        undirectedPair.nodeU() != undirectedPair.nodeV()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试空值处理
     */
    def "测试空值处理"() {
        when: "使用null创建有序端点对"
        EndpointPair.ordered(null, "target")
        
        then: "应抛出NullPointerException"
        thrown(NullPointerException)
        
        when: "使用null创建有序端点对"
        EndpointPair.ordered("source", null)
        
        then: "应抛出NullPointerException"
        thrown(NullPointerException)
        
        when: "使用null创建无序端点对"
        EndpointPair.unordered(null, "nodeV")
        
        then: "应抛出NullPointerException"
        thrown(NullPointerException)
        
        when: "使用null创建无序端点对"
        EndpointPair.unordered("nodeU", null)
        
        then: "应抛出NullPointerException"
        thrown(NullPointerException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试自环（self-loop）端点对
     */
    def "测试自环端点对"() {
        given: "创建有序和无序的自环端点对"
        def orderedSelfLoop = EndpointPair.ordered("A", "A")
        def unorderedSelfLoop = EndpointPair.unordered("B", "B")
        
        expect: "有序自环端点对的source和target应相同"
        orderedSelfLoop.source() == orderedSelfLoop.target()
        orderedSelfLoop.nodeU() == orderedSelfLoop.nodeV()
        
        and: "无序自环端点对的nodeU和nodeV应相同"
        unorderedSelfLoop.nodeU() == unorderedSelfLoop.nodeV()
        
        and: "adjacentNode方法对自环应返回相同节点"
        orderedSelfLoop.adjacentNode("A") == "A"
        unorderedSelfLoop.adjacentNode("B") == "B"
    }
}