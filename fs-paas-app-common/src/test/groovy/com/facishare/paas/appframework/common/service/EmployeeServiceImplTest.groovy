package com.facishare.paas.appframework.common.service

import com.facishare.organization.api.model.RunStatus
import com.facishare.organization.api.model.departmentmember.MainDepartment
import com.facishare.organization.api.model.employee.BatchGetEmployeeIdsByDepartmentId
import com.facishare.organization.api.model.employee.EmployeeDto
import com.facishare.organization.api.model.employee.arg.*
import com.facishare.organization.api.model.employee.result.*
import com.facishare.organization.api.model.search.arg.BatchGetEmployeesDtoByDepartmentIdAndMatchNameArg
import com.facishare.organization.api.model.search.result.BatchGetEmployeesDtoByDepartmentIdAndMatchNameResult
import com.facishare.organization.api.model.type.EmployeeEntityStatus
import com.facishare.organization.api.service.EmployeeProviderService
import com.facishare.organization.api.service.SearchService
import com.facishare.paas.appframework.core.model.User
import com.facishare.social.personnel.PersonnelObjService
import com.facishare.social.personnel.model.FindByUserId
import com.facishare.social.personnel.model.GetPersonnelListByNums
import com.facishare.social.personnel.model.PersonnelDto
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class EmployeeServiceImplTest extends Specification {
    
    EmployeeServiceImpl employeeService
    EmployeeProviderService employeeProviderService = Mock(EmployeeProviderService)
    SearchService searchService = Mock(SearchService)
    PersonnelObjService personnelObjService = Mock(PersonnelObjService)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        employeeService = new EmployeeServiceImpl()
        employeeService.employeeProviderService = employeeProviderService
        employeeService.searchService = searchService
        employeeService.personnelObjService = personnelObjService
    }
    
    def "测试batchGetUserInfo方法 - 正常场景"() {
        given:
        def tenantId = "12345"
        def userIds = ["1001", "1002", "1003"] as Set
        def status = 2
        
        def employeeDto1 = new EmployeeDto()
        employeeDto1.setEmployeeId(1001)
        employeeDto1.setName("张三")
        employeeDto1.setStatus(EmployeeEntityStatus.NORMAL)
        
        def employeeDto2 = new EmployeeDto()
        employeeDto2.setEmployeeId(1002)
        employeeDto2.setName("李四")
        employeeDto2.setStatus(EmployeeEntityStatus.NORMAL)
        
        def employeeDto3 = new EmployeeDto()
        employeeDto3.setEmployeeId(1003)
        employeeDto3.setName("王五")
        employeeDto3.setStatus(EmployeeEntityStatus.NORMAL)
        
        def result = new BatchGetEmployeeDtoResult()
        result.setEmployeeDtos([employeeDto1, employeeDto2, employeeDto3])
        
        when:
        def employees = employeeService.batchGetUserInfo(tenantId, userIds, status)
        
        then:
        1 * employeeProviderService.batchGetEmployeeDto(_ as BatchGetEmployeeDtoArg) >> { args ->
            assert args[0].enterpriseId == 12345
            assert args[0].employeeIds.containsAll([1001, 1002, 1003])
            assert args[0].runStatus == RunStatus.ALL
            return result
        }
        
        employees.size() == 3
        employees[0].employeeId == 1001
        employees[0].name == "张三"
        employees[1].employeeId == 1002
        employees[1].name == "李四"
        employees[2].employeeId == 1003
        employees[2].name == "王五"
    }
    
    def "测试batchGetUserInfo方法 - 空参数场景"() {
        given:
        def tenantId = "12345"
        def userIds = [] as Set
        
        when:
        def employees = employeeService.batchGetUserInfo(tenantId, userIds)
        
        then:
        0 * employeeProviderService.batchGetEmployeeDto(_)
        
        employees != null
        employees.isEmpty()
    }
    
    def "测试batchGetUserInfo方法 - 包含系统用户ID场景"() {
        given:
        def tenantId = "12345"
        def userIds = ["1001", User.SUPPER_ADMIN_USER_ID] as Set
        def status = 2
        
        def employeeDto1 = new EmployeeDto()
        employeeDto1.setEmployeeId(1001)
        employeeDto1.setName("张三")
        employeeDto1.setStatus(EmployeeEntityStatus.NORMAL)
        
        def result = new BatchGetEmployeeDtoResult()
        result.setEmployeeDtos([employeeDto1])
        
        when:
        def employees = employeeService.batchGetUserInfo(tenantId, userIds, status)
        
        then:
        1 * employeeProviderService.batchGetEmployeeDto(_ as BatchGetEmployeeDtoArg) >> result
        
        employees.size() == 2
        employees[0].employeeId == 1001
        employees[0].name == "张三"
        employees[1].employeeId == Integer.parseInt(User.SUPPER_ADMIN_USER_ID)
        employees[1].name != null
        employees[1].status == EmployeeEntityStatus.NORMAL
    }
    
    def "测试getUserInfo方法"() {
        given:
        def tenantId = "12345"
        def userId = "1001"
        
        def employeeDto = new EmployeeDto()
        employeeDto.setEmployeeId(1001)
        employeeDto.setName("张三")
        employeeDto.setStatus(EmployeeEntityStatus.NORMAL)
        
        def result = new GetEmployeeDtoResult()
        result.setEmployeeDto(employeeDto)
        
        when:
        def employee = employeeService.getUserInfo(tenantId, userId)
        
        then:
        1 * employeeProviderService.getEmployeeDto(_ as GetEmployeeDtoArg) >> { args ->
            assert args[0].enterpriseId == 12345
            assert args[0].employeeId == 1001
            return result
        }
        
        employee == employeeDto
        employee.employeeId == 1001
        employee.name == "张三"
    }
    
    def "测试batchGetUserByNickName方法"() {
        given:
        def tenantId = "12345"
        def nickName = "张"
        
        def employeeDto1 = new EmployeeDto()
        employeeDto1.setEmployeeId(1001)
        employeeDto1.setName("张三")
        
        def employeeDto2 = new EmployeeDto()
        employeeDto2.setEmployeeId(1002)
        employeeDto2.setName("张伟")
        
        def result = new BatchGetEmployeesDtoByDepartmentIdAndMatchNameResult()
        result.setEmployeeDtos([employeeDto1, employeeDto2])
        
        when:
        def employees = employeeService.batchGetUserByNickName(tenantId, nickName)
        
        then:
        1 * searchService.batchGetEmployeesByDepartmentIdAndMatchName(_ as BatchGetEmployeesDtoByDepartmentIdAndMatchNameArg) >> { args ->
            assert args[0].enterpriseId == 12345
            assert args[0].name == "张"
            assert args[0].departmentIds.isEmpty()
            assert !args[0].includeLowDepartment
            assert args[0].runStatus == RunStatus.ALL
            return result
        }
        
        employees.size() == 2
        employees[0].employeeId == 1001
        employees[0].name == "张三"
        employees[1].employeeId == 1002
        employees[1].name == "张伟"
    }
    
    def "测试batchGetUserByNickNames方法"() {
        given:
        def tenantId = "12345"
        def nickNames = ["张三", "李四"]
        def status = 2
        
        def employeeDto1 = new EmployeeDto()
        employeeDto1.setEmployeeId(1001)
        employeeDto1.setName("张三")
        
        def employeeDto2 = new EmployeeDto()
        employeeDto2.setEmployeeId(1002)
        employeeDto2.setName("李四")
        
        def result = new GetEmployeeByNamesResult()
        result.setEmployeeDtos([employeeDto1, employeeDto2])
        
        when:
        def employees = employeeService.batchGetUserByNickNames(tenantId, nickNames, status)
        
        then:
        1 * employeeProviderService.getEmployeeByNames(_ as GetEmployeeByNamesArg) >> { args ->
            assert args[0].enterpriseId == 12345
            assert args[0].names == ["张三", "李四"]
            assert args[0].runStatus == RunStatus.ALL
            return result
        }
        
        employees.size() == 2
        employees[0].employeeId == 1001
        employees[0].name == "张三"
        employees[1].employeeId == 1002
        employees[1].name == "李四"
    }
    
    def "测试batchGetUserByCodes方法"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        def codes = ["A001", "A002"]
        
        def personnelDto1 = new PersonnelDto()
        personnelDto1.setEmpId("1001")
        personnelDto1.setEmpNum("A001")
        personnelDto1.setName("张三")
        
        def personnelDto2 = new PersonnelDto()
        personnelDto2.setEmpId("1002")
        personnelDto2.setEmpNum("A002")
        personnelDto2.setName("李四")
        
        def result = new GetPersonnelListByNums.Result()
        result.setList([personnelDto1, personnelDto2])
        
        when:
        def employees = employeeService.batchGetUserByCodes(user, codes)
        
        then:
        1 * personnelObjService.getPersonnelListByNums(_ as GetPersonnelListByNums.Argument) >> { args ->
            assert args[0].tenantId == "12345"
            assert args[0].empNums == ["A001", "A002"]
            return result
        }
        
        employees.size() == 2
        employees[0].empId == "1001"
        employees[0].empNum == "A001"
        employees[0].name == "张三"
        employees[1].empId == "1002"
        employees[1].empNum == "A002"
        employees[1].name == "李四"
    }
    
    def "测试batchGetEmployeeIdsByDeptIds方法"() {
        given:
        def tenantId = "12345"
        def deptIds = ["2001", "2002"]
        def userStatus = 2
        def includeLowDepartment = true
        
        def result = new BatchGetEmployeeIdsByDepartmentId.Result()
        result.setEmployeeIds([1001, 1002, 1003])
        
        when:
        def employeeIds = employeeService.batchGetEmployeeIdsByDeptIds(tenantId, deptIds, userStatus, includeLowDepartment)
        
        then:
        1 * employeeProviderService.batchGetEmployeeIdsByDepartmentId(_ as BatchGetEmployeeIdsByDepartmentId.Arg) >> { args ->
            assert args[0].enterpriseId == 12345
            assert args[0].departmentIds == [2001, 2002]
            assert args[0].includeLowDepartment
            assert args[0].mainDepartment == MainDepartment.ALL
            assert args[0].runStatus == RunStatus.ALL
            return result
        }
        
        employeeIds.size() == 3
        employeeIds.containsAll(["1001", "1002", "1003"])
    }
    
    def "测试querySubordinatesByUserId方法 - 级联"() {
        given:
        def tenantId = "12345"
        def userId = "1001"
        def status = 2
        def cascade = true
        
        def employeeDto1 = new EmployeeDto()
        employeeDto1.setEmployeeId(1002)
        employeeDto1.setName("张三")
        
        def employeeDto2 = new EmployeeDto()
        employeeDto2.setEmployeeId(1003)
        employeeDto2.setName("李四")
        
        def result = new GetAllSubordinateEmployeesDtoResult()
        result.setEmployeeDtos([employeeDto1, employeeDto2])
        
        when:
        def employees = employeeService.querySubordinatesByUserId(tenantId, userId, status, cascade)
        
        then:
        1 * employeeProviderService.getAllSubordinateEmployees(_ as GetAllSubordinateEmployeesDtoArg) >> { args ->
            assert args[0].enterpriseId == 12345
            assert args[0].employeeId == 1001
            assert args[0].runStatus == RunStatus.ALL
            return result
        }
        
        employees.size() == 2
        employees[0].employeeId == 1002
        employees[0].name == "张三"
        employees[1].employeeId == 1003
        employees[1].name == "李四"
    }
    
    def "测试querySubordinatesByUserId方法 - 不级联"() {
        given:
        def tenantId = "12345"
        def userId = "1001"
        def status = 2
        def cascade = false
        
        def employeeDto1 = new EmployeeDto()
        employeeDto1.setEmployeeId(1002)
        employeeDto1.setName("张三")
        
        def result = new GetSubordinateEmployeesDtoResult()
        result.setEmployeeDtos([employeeDto1])
        
        when:
        def employees = employeeService.querySubordinatesByUserId(tenantId, userId, status, cascade)
        
        then:
        1 * employeeProviderService.getSubordinateEmployees(_ as GetSubordinateEmployeesDtoArg) >> { args ->
            assert args[0].enterpriseId == 12345
            assert args[0].employeeId == 1001
            assert args[0].runStatus == RunStatus.ALL
            return result
        }
        
        employees.size() == 1
        employees[0].employeeId == 1002
        employees[0].name == "张三"
    }
    
    def "测试convertStatusToRunStatus方法"() {
        when:
        def result = Whitebox.invokeMethod(employeeService, "convertStatusToRunStatus", status)
        
        then:
        result == expected
        
        where:
        status || expected
        1      || RunStatus.AVAILABLE
        2      || RunStatus.ALL
        3      || RunStatus.DISABLED
        null   || RunStatus.ALL
    }
} 