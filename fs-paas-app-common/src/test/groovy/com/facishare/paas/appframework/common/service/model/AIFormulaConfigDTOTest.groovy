package com.facishare.paas.appframework.common.service.model

import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * 测试内容描述：AIFormulaConfigDTO类的单元测试
 */
class AIFormulaConfigDTOTest extends Specification {

    def "test default constructor"() {
        when: "创建默认构造函数实例"
        def config = new AIFormulaConfigDTO()

        then: "验证默认值"
        config.fieldN == null
        config.optionN == null
        config.temperature == null
        config.maxTokens == null
        config.model == null
        config.fieldTypeExplain == null
        config.noSupportFields != null
        config.subNoSupportTypes != null
        config.noSupportApis != null
        config.noSupportTypes != null
        config.noSupportGlobsVars != null
    }

    def "test builder pattern"() {
        when: "使用builder创建实例"
        def config = AIFormulaConfigDTO.builder()
                .fieldN(30)
                .optionN(10)
                .temperature(0.5)
                .maxTokens(1024)
                .model("test-model")
                .fieldTypeExplain(Maps.newHashMap())
                .build()

        then: "验证设置的值"
        config.fieldN == 30
        config.optionN == 10
        config.temperature == 0.5
        config.maxTokens == 1024
        config.model == "test-model"
        config.fieldTypeExplain != null
    }

    def "test DEFAULT constant"() {
        when: "获取默认配置"
        def defaultConfig = AIFormulaConfigDTO.DEFAULT

        then: "验证默认配置值"
        defaultConfig.fieldN == 50
        defaultConfig.optionN == 5
        defaultConfig.temperature == 0.0
        defaultConfig.maxTokens == 2048
        defaultConfig.model == "qwen-plus"
        defaultConfig.fieldTypeExplain != null
    }

    @Unroll
    def "test getFieldTypeExplain with #fieldType"() {
        given: "创建配置实例"
        def explainMap = Maps.newHashMap()
        explainMap.put("number", "数字")
        explainMap.put("text", "文本")
        
        def config = AIFormulaConfigDTO.builder()
                .fieldTypeExplain(explainMap)
                .build()

        when: "调用getFieldTypeExplain方法"
        def result = config.getFieldTypeExplain(fieldType)

        then: "验证返回值"
        result == expected

        where:
        fieldType | expected
        "number"  | "数字"
        "text"    | "文本"
        "unknown" | "unknown"
        null      | null
    }

    def "test getFieldTypeExplain with null fieldTypeExplain"() {
        given: "创建fieldTypeExplain为null的配置"
        def config = AIFormulaConfigDTO.builder()
                .fieldTypeExplain(null)
                .build()

        when: "调用getFieldTypeExplain方法"
        def result = config.getFieldTypeExplain("test")

        then: "应该返回原始值"
        result == "test"
    }

    @Unroll
    def "test supportApi with api=#api, describeApi=#describeApi"() {
        given: "创建配置实例"
        def noSupportApis = Sets.newHashSet("owner_department", "data_own_department")
        def noSupportFields = Maps.newHashMap()
        noSupportFields.put("TestObj", Sets.newHashSet("blocked_field"))
        
        def config = AIFormulaConfigDTO.builder()
                .noSupportApis(noSupportApis)
                .noSupportFields(noSupportFields)
                .build()

        when: "调用supportApi方法"
        def result = config.supportApi(api, describeApi)

        then: "验证返回值"
        result == expected

        where:
        api               | describeApi | expected
        "valid_api"       | "TestObj"   | true
        "owner_department"| "TestObj"   | false
        "blocked_field"   | "TestObj"   | false
        "valid_api"       | "OtherObj"  | true
        null              | "TestObj"   | false
        "valid_api"       | null        | false
        ""                | "TestObj"   | false
        "valid_api"       | ""          | false
    }

    @Unroll
    def "test supportGlobsVar with api=#api"() {
        given: "创建配置实例"
        def noSupportGlobsVars = Sets.newHashSet("currentTime__g", "currentDate__g")
        def config = AIFormulaConfigDTO.builder()
                .noSupportGlobsVars(noSupportGlobsVars)
                .build()

        when: "调用supportGlobsVar方法"
        def result = config.supportGlobsVar(api)

        then: "验证返回值"
        result == expected

        where:
        api              | expected
        "valid_var"      | true
        "currentTime__g" | false
        "currentDate__g" | false
        null             | false
        ""               | false
    }

    @Unroll
    def "test supportType with type=#type, isSub=#isSub"() {
        given: "创建配置实例"
        def noSupportTypes = Sets.newHashSet("group", "image", "file_attachment")
        def subNoSupportTypes = Sets.newHashSet("master_detail", "object_reference")
        def config = AIFormulaConfigDTO.builder()
                .noSupportTypes(noSupportTypes)
                .subNoSupportTypes(subNoSupportTypes)
                .build()

        when: "调用supportType方法"
        def result = config.supportType(type, isSub)

        then: "验证返回值"
        result == expected

        where:
        type              | isSub | expected
        "text"            | false | true
        "text"            | true  | true
        "group"           | false | false
        "group"           | true  | false
        "master_detail"   | false | true
        "master_detail"   | true  | false
        "object_reference"| false | true
        "object_reference"| true  | false
        null              | false | false
        ""                | false | false
    }

    def "test noSupportTypes default values"() {
        given: "创建默认配置"
        def config = AIFormulaConfigDTO.builder().build()

        expect: "验证默认的不支持类型"
        config.noSupportTypes.contains("group")
        config.noSupportTypes.contains("image")
        config.noSupportTypes.contains("file_attachment")
        config.noSupportTypes.contains("big_file_attachment")
        config.noSupportTypes.contains("select_many")
        config.noSupportTypes.contains("signature")
        config.noSupportTypes.contains("quote")
        config.noSupportTypes.contains("html_rich_text")
        config.noSupportTypes.contains("rich_text")
        config.noSupportTypes.contains("object_reference_many")
    }

    def "test subNoSupportTypes default values"() {
        given: "创建默认配置"
        def config = AIFormulaConfigDTO.builder().build()

        expect: "验证默认的子对象不支持类型"
        config.subNoSupportTypes.contains("master_detail")
        config.subNoSupportTypes.contains("object_reference")
        config.subNoSupportTypes.contains("employee")
        config.subNoSupportTypes.contains("department")
    }

    def "test noSupportApis default values"() {
        given: "创建默认配置"
        def config = AIFormulaConfigDTO.builder().build()

        expect: "验证默认的不支持API"
        config.noSupportApis.contains("owner_department")
        config.noSupportApis.contains("data_own_department")
        config.noSupportApis.contains("data_own_organization")
    }

    def "test noSupportGlobsVars default values"() {
        given: "创建默认配置"
        def config = AIFormulaConfigDTO.builder().build()

        expect: "验证默认的不支持全局变量"
        config.noSupportGlobsVars.contains("currentTime__g")
        config.noSupportGlobsVars.contains("currentDate__g")
        config.noSupportGlobsVars.contains("currentDateTime__g")
    }

    def "test GENERATE_EXPRESSION constant"() {
        expect: "验证GENERATE_EXPRESSION常量存在且不为空"
        AIFormulaConfigDTO.GENERATE_EXPRESSION != null
        AIFormulaConfigDTO.GENERATE_EXPRESSION.length() > 0
        AIFormulaConfigDTO.GENERATE_EXPRESSION.contains("可选字段")
        AIFormulaConfigDTO.GENERATE_EXPRESSION.contains("用户指令")
    }
}
