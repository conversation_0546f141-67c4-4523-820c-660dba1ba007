package com.facishare.paas.appframework.common.graph

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：ValueGraphBuilder值图构建器的单元测试
 */
class ValueGraphBuilderTest extends Specification {

    def "test directed value graph builder"() {
        when: "创建有向值图构建器"
        def builder = ValueGraphBuilder.directed()

        then: "应该创建有向值图构建器"
        builder != null
    }

    def "test undirected value graph builder"() {
        when: "创建无向值图构建器"
        def builder = ValueGraphBuilder.undirected()

        then: "应该创建无向值图构建器"
        builder != null
    }

    def "test build directed value graph"() {
        when: "构建有向值图"
        def graph = ValueGraphBuilder.directed().build()

        then: "应该创建有向值图"
        graph != null
        graph instanceof MutableValueGraph
        graph.isDirected()
        !graph.allowsSelfLoops()
        graph.nodeOrder() == ElementOrder.insertion()
    }

    def "test build undirected value graph"() {
        when: "构建无向值图"
        def graph = ValueGraphBuilder.undirected().build()

        then: "应该创建无向值图"
        graph != null
        graph instanceof MutableValueGraph
        !graph.isDirected()
        !graph.allowsSelfLoops()
        graph.nodeOrder() == ElementOrder.insertion()
    }

    def "test allowsSelfLoops configuration"() {
        when: "配置允许自环"
        def graph = ValueGraphBuilder.directed()
                .allowsSelfLoops(true)
                .build()

        then: "图应该允许自环"
        graph.allowsSelfLoops()

        when: "配置不允许自环"
        def graph2 = ValueGraphBuilder.directed()
                .allowsSelfLoops(false)
                .build()

        then: "图应该不允许自环"
        !graph2.allowsSelfLoops()
    }

    def "test expectedNodeCount configuration"() {
        when: "配置期望节点数"
        def graph = ValueGraphBuilder.directed()
                .expectedNodeCount(100)
                .build()

        then: "应该成功构建图"
        graph != null
        graph.nodes().isEmpty()
    }

    def "test expectedNodeCount with negative value throws exception"() {
        when: "使用负数配置期望节点数"
        ValueGraphBuilder.directed().expectedNodeCount(-1)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test nodeOrder configuration"() {
        when: "配置节点顺序为无序"
        def graph = ValueGraphBuilder.directed()
                .nodeOrder(ElementOrder.unordered())
                .build()

        then: "图应该使用无序节点顺序"
        graph.nodeOrder().type() == ElementOrder.Type.UNORDERED

        when: "配置节点顺序为自然顺序"
        def graph2 = ValueGraphBuilder.directed()
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        then: "图应该使用自然顺序"
        graph2.nodeOrder().type() == ElementOrder.Type.SORTED
    }

    def "test from existing value graph"() {
        given: "创建一个现有值图"
        def originalGraph = ValueGraphBuilder.directed()
                .allowsSelfLoops(true)
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        when: "从现有值图创建构建器"
        def newGraph = ValueGraphBuilder.from(originalGraph).build()

        then: "新图应该继承原图的属性"
        newGraph.isDirected() == originalGraph.isDirected()
        newGraph.allowsSelfLoops() == originalGraph.allowsSelfLoops()
        newGraph.nodeOrder().type() == originalGraph.nodeOrder().type()
    }

    def "test builder method chaining"() {
        when: "链式调用构建器方法"
        def graph = ValueGraphBuilder.directed()
                .allowsSelfLoops(true)
                .expectedNodeCount(50)
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        then: "应该正确应用所有配置"
        graph.isDirected()
        graph.allowsSelfLoops()
        graph.nodeOrder().type() == ElementOrder.Type.SORTED
    }

    def "test multiple builds from same builder"() {
        given: "创建构建器"
        def builder = ValueGraphBuilder.directed().allowsSelfLoops(true)

        when: "多次构建图"
        def graph1 = builder.build()
        def graph2 = builder.build()

        then: "应该创建不同的图实例但具有相同属性"
        graph1 != graph2
        graph1.isDirected() == graph2.isDirected()
        graph1.allowsSelfLoops() == graph2.allowsSelfLoops()
    }

    def "test value graph functionality after build"() {
        given: "构建值图"
        def graph = ValueGraphBuilder.undirected().build()

        when: "添加节点和带值的边"
        graph.addNode("A")
        graph.addNode("B")
        graph.putEdgeValue("A", "B", "edge_value")

        then: "图应该正确工作"
        graph.nodes().size() == 2
        graph.nodes().contains("A")
        graph.nodes().contains("B")
        graph.hasEdgeConnecting("A", "B")
        graph.hasEdgeConnecting("B", "A") // 无向图
        graph.edgeValue("A", "B").get() == "edge_value"
        graph.edgeValue("B", "A").get() == "edge_value" // 无向图
    }

    def "test self loop behavior in value graph"() {
        given: "创建允许自环的值图"
        def allowSelfLoopGraph = ValueGraphBuilder.directed()
                .allowsSelfLoops(true)
                .build()

        and: "创建不允许自环的值图"
        def noSelfLoopGraph = ValueGraphBuilder.directed()
                .allowsSelfLoops(false)
                .build()

        when: "在允许自环的图中添加自环"
        allowSelfLoopGraph.addNode("A")
        allowSelfLoopGraph.putEdgeValue("A", "A", "self_loop_value")

        then: "应该成功添加自环"
        allowSelfLoopGraph.hasEdgeConnecting("A", "A")
        allowSelfLoopGraph.edgeValue("A", "A").get() == "self_loop_value"

        when: "在不允许自环的图中添加自环"
        noSelfLoopGraph.addNode("B")
        noSelfLoopGraph.putEdgeValue("B", "B", "value")

        then: "应该抛出异常"
        thrown(IllegalArgumentException)
    }

    def "test node order behavior in value graph"() {
        given: "创建使用自然顺序的值图"
        def naturalOrderGraph = ValueGraphBuilder.directed()
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        when: "以非字母顺序添加节点"
        naturalOrderGraph.addNode("C")
        naturalOrderGraph.addNode("A")
        naturalOrderGraph.addNode("B")

        then: "节点应该按自然顺序排列"
        naturalOrderGraph.nodes() as List == ["A", "B", "C"]
    }

    def "test insertion order behavior in value graph"() {
        given: "创建使用插入顺序的值图"
        def insertionOrderGraph = ValueGraphBuilder.directed()
                .nodeOrder(ElementOrder.insertion())
                .build()

        when: "添加节点"
        insertionOrderGraph.addNode("C")
        insertionOrderGraph.addNode("A")
        insertionOrderGraph.addNode("B")

        then: "节点应该按插入顺序排列"
        insertionOrderGraph.nodes() as List == ["C", "A", "B"]
    }

    def "test null nodeOrder throws exception"() {
        when: "使用null节点顺序"
        ValueGraphBuilder.directed().nodeOrder(null)

        then: "应该抛出NullPointerException"
        thrown(NullPointerException)
    }

    def "test builder immutability"() {
        given: "创建构建器"
        def builder = ValueGraphBuilder.directed()

        when: "配置构建器"
        def modifiedBuilder = builder.allowsSelfLoops(true)

        then: "应该返回相同的构建器实例（流式API）"
        modifiedBuilder.is(builder)
    }

    def "test type safety with generics"() {
        when: "创建字符串节点和整数值的图"
        def stringIntGraph = ValueGraphBuilder.directed().<String, Integer>build()

        and: "创建整数节点和字符串值的图"
        def intStringGraph = ValueGraphBuilder.directed().<Integer, String>build()

        then: "类型应该正确"
        stringIntGraph instanceof MutableValueGraph<String, Integer>
        intStringGraph instanceof MutableValueGraph<Integer, String>
    }

    def "test complex value graph construction"() {
        given: "构建复杂值图"
        def graph = ValueGraphBuilder.directed()
                .allowsSelfLoops(true)
                .expectedNodeCount(10)
                .nodeOrder(ElementOrder.<String>natural())
                .build()

        when: "添加多个节点和带值的边"
        ["D", "A", "C", "B"].each { graph.addNode(it) }
        graph.putEdgeValue("A", "B", 1.0)
        graph.putEdgeValue("B", "C", 2.0)
        graph.putEdgeValue("C", "D", 3.0)
        graph.putEdgeValue("A", "A", 0.0) // 自环

        then: "图应该正确构建"
        graph.nodes() as List == ["A", "B", "C", "D"] // 自然顺序
        graph.edges().size() == 4
        graph.hasEdgeConnecting("A", "A") // 自环存在
        graph.edgeValue("A", "B").get() == 1.0
        graph.edgeValue("B", "C").get() == 2.0
        graph.edgeValue("C", "D").get() == 3.0
        graph.edgeValue("A", "A").get() == 0.0
        graph.successors("A").contains("B")
        graph.successors("A").contains("A")
    }

    def "test edge value operations"() {
        given: "创建值图"
        def graph = ValueGraphBuilder.undirected().build()
        graph.addNode("A")
        graph.addNode("B")

        when: "添加边值"
        graph.putEdgeValue("A", "B", "initial_value")

        then: "应该能够获取边值"
        graph.edgeValue("A", "B").isPresent()
        graph.edgeValue("A", "B").get() == "initial_value"
        graph.edgeValueOrDefault("A", "B", "default") == "initial_value"

        when: "更新边值"
        graph.putEdgeValue("A", "B", "updated_value")

        then: "边值应该被更新"
        graph.edgeValue("A", "B").get() == "updated_value"

        when: "移除边"
        graph.removeEdge("A", "B")

        then: "边值应该不存在"
        !graph.edgeValue("A", "B").isPresent()
        graph.edgeValueOrDefault("A", "B", "default") == "default"
    }

    def "test edge cases"() {
        expect: "验证边界情况"
        // 零期望节点数
        ValueGraphBuilder.directed().expectedNodeCount(0).build() != null

        // 大期望节点数
        ValueGraphBuilder.directed().expectedNodeCount(1000000).build() != null

        // 多次配置相同属性
        def graph = ValueGraphBuilder.directed()
                .allowsSelfLoops(true)
                .allowsSelfLoops(false)
                .build()
        !graph.allowsSelfLoops() // 最后一次配置生效
    }
}
