package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.AuthContext
import com.facishare.paas.appframework.common.service.dto.QueryRoleInfoByCodes
import com.facishare.paas.appframework.common.service.dto.QueryUserRoleInfo
import com.facishare.paas.appframework.core.exception.PermissionError
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class UserRoleInformationServiceImplTest extends Specification {

    UserRoleInformationServiceImpl userRoleInformationService
    UserRoleInformationProxy userRoleInformationProxy

    def setupSpec() {
        def i18nClient = Mock(com.fxiaoke.i18n.client.I18nClient)
        def i18nServiceImpl = Mock(com.fxiaoke.i18n.client.impl.I18nServiceImpl)
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(com.fxiaoke.i18n.client.I18nClient, "SINGLETON", i18nClient)
        i18nClient.getAllLanguage() >> []
    }

    def setup() {
        userRoleInformationProxy = Mock(UserRoleInformationProxy)
        
        // 通过构造函数设置代理，确保不为null
        userRoleInformationService = new UserRoleInformationServiceImpl()
        
        // 使用反射直接设置属性而不是字段
        Whitebox.setInternalState(userRoleInformationService, "userRoleInformationProxy", userRoleInformationProxy)
        
        // 再次验证代理已正确设置且不为null
        assert userRoleInformationService.userRoleInformationProxy != null
        
        // 模拟静态方法
        GroovyMock(RestUtils, global: true)
    }

    def "测试queryRoleInfoByRoleCode方法，返回成功结果"() {
        given: "准备用户和角色ID"
        def user = Mock(User)
        user.getTenantId() >> "tenant123"
        def roleIds = ["admin", "user", "manager"]

        and: "模拟RestUtils.buildHeaders方法"
        def headers = ["X-FS-TENANT-ID": "tenant123"]
        RestUtils.buildHeaders(user) >> headers

        and: "模拟代理调用结果"
        def roleInfoList = [
                new QueryUserRoleInfo.RoleInfo(roleCode: "admin", roleName: "管理员"),
                new QueryUserRoleInfo.RoleInfo(roleCode: "user", roleName: "普通用户"),
                new QueryUserRoleInfo.RoleInfo(roleCode: "manager", roleName: "经理")
        ]

        // 使用Mock代替直接创建复杂对象
        def result = Mock(QueryRoleInfoByCodes.Result)
        result.isSuccess() >> true
        result.getRoles() >> roleInfoList

        userRoleInformationProxy.queryRoleInfoWithCodes(_, headers) >> result

        when: "调用查询方法"
        def response = userRoleInformationService.queryRoleInfoByRoleCode(user, roleIds)

        then: "返回正确的角色信息"
        response.size() == 3
        response[0].roleCode == "admin"
        response[0].roleName == "管理员"
        response[1].roleCode == "user"
        response[1].roleName == "普通用户"
        response[2].roleCode == "manager"
        response[2].roleName == "经理"
    }

    def "测试queryRoleInfoByRoleCode方法，传入空角色ID列表"() {
        given: "准备用户和空角色ID列表"
        def user = Mock(User)

        when: "调用查询方法"
        def response = userRoleInformationService.queryRoleInfoByRoleCode(user, [])

        then: "返回空列表"
        response.size() == 0
        0 * userRoleInformationProxy.queryRoleInfoWithCodes(_, _)
    }

    def "测试queryRoleInfoByRoleCode方法，传入null角色ID列表"() {
        given: "准备用户和null角色ID列表"
        def user = Mock(User)

        when: "调用查询方法"
        def response = userRoleInformationService.queryRoleInfoByRoleCode(user, null)

        then: "返回空列表"
        response.size() == 0
        0 * userRoleInformationProxy.queryRoleInfoWithCodes(_, _)
    }

    def "测试queryRoleInfoByRoleCode方法，代理调用失败"() {
        given: "准备用户和角色ID"
        def user = Mock(User)
        user.getTenantId() >> "tenant123"
        def roleIds = ["admin", "user"]

        and: "模拟RestUtils.buildHeaders方法"
        def headers = ["X-FS-TENANT-ID": "tenant123"]
        RestUtils.buildHeaders(user) >> headers

        and: "模拟代理调用结果失败"
        def result = Mock(QueryRoleInfoByCodes.Result)
        result.isSuccess() >> false
        result.getErrMessage() >> "权限错误"

        userRoleInformationProxy.queryRoleInfoWithCodes(_, headers) >> result

        when: "调用查询方法"
        userRoleInformationService.queryRoleInfoByRoleCode(user, roleIds)

        then: "抛出PermissionError异常"
        def e = thrown(PermissionError)
        e.message == "权限错误"
    }

    def "测试queryRoleInfoByRoleCode方法，代理返回null角色列表"() {
        given: "准备用户和角色ID"
        def user = Mock(User)
        user.getTenantId() >> "tenant123"
        def roleIds = ["admin", "user"]

        and: "模拟RestUtils.buildHeaders方法"
        def headers = ["X-FS-TENANT-ID": "tenant123"]
        RestUtils.buildHeaders(user) >> headers

        and: "模拟代理调用结果成功但角色列表为null"
        def result = Mock(QueryRoleInfoByCodes.Result)
        result.isSuccess() >> true
        result.getRoles() >> null

        userRoleInformationProxy.queryRoleInfoWithCodes(_, headers) >> result

        when: "调用查询方法"
        def response = userRoleInformationService.queryRoleInfoByRoleCode(user, roleIds)

        then: "返回空列表"
        response.size() == 0
    }

    def "测试AuthContext.buildByUser方法"() {
        given: "准备用户"
        def user = Mock(User)
        user.getTenantId() >> "tenant123"
        user.getUserId() >> "user456"
        user.isOutUser() >> false

        when: "构建AuthContext"
        def authContext = AuthContext.buildByUser(user)

        then: "AuthContext包含正确的租户ID和用户ID"
        authContext.tenantId == "tenant123"
        authContext.userId == "user456"
    }
} 