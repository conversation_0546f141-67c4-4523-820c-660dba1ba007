package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.SendTextMessage
import com.facishare.paas.appframework.common.service.model.CRMNotification
import com.facishare.paas.appframework.common.service.model.NewCrmNotification
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class MessagePlatformServiceImplTest extends Specification {
    
    MessagePlatformServiceImpl messagePlatformService
    MessagePlatformProxy platformProxy = Mock(MessagePlatformProxy)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        messagePlatformService = new MessagePlatformServiceImpl()
        messagePlatformService.platformProxy = platformProxy
    }
    
    def "测试sendTextMessage方法 - 无FixContent2ID"() {
        given:
        def user = Mock(User)
        user.getTenantIdInt() >> 12345
        
        def notification = CRMNotification.builder()
                .sender("1001")
                .content("测试内容")
                .title("测试标题")
                .receiverIds([1001, 1002] as Set)
                .build()
        
        def responseResult = new SendTextMessage.Result()
        responseResult.code = "0"
        
        def headers = [header1: "value1"]
        
        when:
        messagePlatformService.sendTextMessage(user, notification)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * platformProxy.sendTextMessage(headers, _) >> responseResult
    }
    
    def "测试sendTextMessage方法 - 带FixContent2ID"() {
        given:
        def user = Mock(User)
        user.getTenantIdInt() >> 12345
        
        def notification = CRMNotification.builder()
                .sender("1001")
                .content("测试内容")
                .title("测试标题")
                .receiverIds([1001, 1002] as Set)
                .fixContent2ID("fixContent")
                .objectApiName("AccountObj")
                .dataId("123456")
                .build()
        
        def responseResult = new SendTextMessage.Result()
        responseResult.code = "0"
        
        def headers = [header1: "value1"]
        
        when:
        messagePlatformService.sendTextMessage(user, notification)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * platformProxy.sendTextLinkMessage(headers, _) >> responseResult
    }
    
    def "测试sendPlatFormMessage方法 - 文本消息"() {
        given:
        def user = Mock(User)
        user.getTenantIdInt() >> 12345
        user.isOutUser() >> false
        
        def notification = NewCrmNotification.builder()
                .senderId("1001")
                .title("测试标题")
                .fullContent("测试内容")
                .receiverIDs([1001, 1002] as Set)
                .urlType(0) // 文本消息
                .build()
        
        def responseResult = new SendTextMessage.Result()
        responseResult.code = "0"
        
        def headers = [header1: "value1"]
        
        when:
        messagePlatformService.sendPlatFormMessage(user, notification)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * platformProxy.sendTextMessage(headers, _) >> responseResult
    }
    
    def "测试sendPlatFormMessage方法 - 链接消息"() {
        given:
        def user = Mock(User)
        user.getTenantIdInt() >> 12345
        user.isOutUser() >> false
        
        def notification = NewCrmNotification.builder()
                .senderId("1001")
                .title("测试标题")
                .fullContent("测试内容")
                .receiverIDs([1001, 1002] as Set)
                .urlType(1) // 非文本消息
                .objectApiName("AccountObj")
                .objectId("123456")
                .build()
        
        def responseResult = new SendTextMessage.Result()
        responseResult.code = "0"
        
        def headers = [header1: "value1"]
        
        when:
        messagePlatformService.sendPlatFormMessage(user, notification)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * platformProxy.sendTextLinkMessage(headers, _) >> responseResult
    }
    
    def "测试sendPlatFormMessage方法 - 批量提醒消息"() {
        given:
        def user = Mock(User)
        user.getTenantIdInt() >> 12345
        user.isOutUser() >> false
        
        def notification = NewCrmNotification.builder()
                .senderId("1001")
                .title("测试标题")
                .fullContent("测试内容")
                .receiverIDs([1001, 1002] as Set)
                .urlType(1) // 非文本消息
                .objectApiName("AccountObj")
                .objectId("123456")
                .type(NewCrmNotification.CUSTOM_REMIND_JOB_SCHEDULE_TYPE)
                .urlParameter([key1: "value1"])
                .build()
        
        def responseResult = new SendTextMessage.Result()
        responseResult.code = "0"
        
        def headers = [header1: "value1"]
        
        when:
        messagePlatformService.sendPlatFormMessage(user, notification)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * platformProxy.sendTextLinkMessage(headers, _) >> responseResult
    }
    
    def "测试sendTextMessage发送失败情况"() {
        given:
        def user = Mock(User)
        user.getTenantIdInt() >> 12345
        
        def notification = CRMNotification.builder()
                .sender("1001")
                .content("测试内容")
                .title("测试标题")
                .receiverIds([1001, 1002] as Set)
                .build()
        
        def responseResult = new SendTextMessage.Result()
        responseResult.code = "1001"
        responseResult.message = "发送失败"
        
        def headers = [header1: "value1"]
        
        when:
        messagePlatformService.sendTextMessage(user, notification)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * platformProxy.sendTextMessage(headers, _) >> responseResult
    }
    
    def "测试外部用户发送消息"() {
        given:
        def user = Mock(User)
        user.getTenantIdInt() >> 12345
        user.isOutUser() >> true
        
        def notification = NewCrmNotification.builder()
                .senderId("1001")
                .title("测试标题")
                .fullContent("测试内容")
                .receiverIDs([1001, 1002] as Set)
                .urlType(0)
                .build()
        
        def responseResult = new SendTextMessage.Result()
        responseResult.code = "0"
        
        def headers = [header1: "value1"]
        
        when:
        messagePlatformService.sendPlatFormMessage(user, notification)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * platformProxy.sendTextMessage(headers, { arg ->
            assert arg.outEmployees == notification.receiverIDs
            true
        }) >> responseResult
    }
} 