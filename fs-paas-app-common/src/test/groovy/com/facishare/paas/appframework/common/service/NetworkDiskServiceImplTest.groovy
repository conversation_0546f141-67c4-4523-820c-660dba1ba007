package com.facishare.paas.appframework.common.service

import com.facishare.netdisk.api.model.NSGetDownloadTokenArg
import com.facishare.netdisk.api.model.NSGetDownloadTokenResult
import com.facishare.netdisk.api.model.NSGetFileInfoByNPathArg
import com.facishare.netdisk.api.model.NSGetFileInfoByNPathResult
import com.facishare.netdisk.api.model.NSGetFoldersByParentIDArg
import com.facishare.netdisk.api.model.NSGetFoldersByParentIDResult
import com.facishare.netdisk.api.model.base.Operator
import com.facishare.netdisk.api.model.type.V5FileInfo
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.facishare.paas.metadata.support.GDSHandler
import com.facishare.warehouse.api.dubbo.FilePackedService
import com.facishare.warehouse.api.model.FilePackedArg
import com.facishare.warehouse.api.model.FilePackedResult
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class NetworkDiskServiceImplTest extends Specification {
    
    NetworkDiskServiceImpl networkDiskService
    NetworkDiskProxy networkDiskProxy = Mock(NetworkDiskProxy)
    FilePackedService filePackedService = Mock(FilePackedService)
    GDSHandler gdsHandler = Mock(GDSHandler)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        networkDiskService = new NetworkDiskServiceImpl()
        networkDiskService.networkDiskProxy = networkDiskProxy
        networkDiskService.filePackedService = filePackedService
        networkDiskService.gdsHandler = gdsHandler
    }
    
    def "测试getFileInfoByNPath方法 - 通过用户参数"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        
        def paths = ["path1", "path2"]
        def ea = "test_ea"
        def headers = [header1: "value1"]
        
        def fileInfo1 = Mock(V5FileInfo)
        def fileInfo2 = Mock(V5FileInfo)
        def fileInfoList = [fileInfo1, fileInfo2]
        
        def result = new NSGetFileInfoByNPathResult()
        result.fileInfoList = fileInfoList
        
        when:
        def returnValue = networkDiskService.getFileInfoByNPath(user, paths)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * RestUtils.buildHeaders(user) >> headers
        1 * networkDiskProxy.getFieldInfoByNPath(headers, _) >> { args ->
            NSGetFileInfoByNPathArg arg = args[1]
            assert arg.nPathList == paths
            assert arg.operator.enterpriseId == Integer.parseInt(tenantId)
            assert arg.operator.employeeId == Integer.parseInt(userId)
            assert arg.operator.enterpriseAccount == ea
            result
        }
        
        returnValue.size() == 2
        returnValue == fileInfoList
    }
    
    def "测试getFileInfoByNPath方法 - 通过详细参数"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getUserId() >> userId
        
        def paths = ["path1", "path2"]
        def ea = "test_ea"
        def headers = [header1: "value1"]
        
        def fileInfo1 = Mock(V5FileInfo)
        def fileInfo2 = Mock(V5FileInfo)
        def fileInfoList = [fileInfo1, fileInfo2]
        
        def result = new NSGetFileInfoByNPathResult()
        result.fileInfoList = fileInfoList
        
        when:
        def returnValue = networkDiskService.getFileInfoByNPath(paths, tenantId, ea, user)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * networkDiskProxy.getFieldInfoByNPath(headers, _) >> { args ->
            NSGetFileInfoByNPathArg arg = args[1]
            assert arg.nPathList == paths
            assert arg.operator.enterpriseId == Integer.parseInt(tenantId)
            assert arg.operator.employeeId == Integer.parseInt(userId)
            assert arg.operator.enterpriseAccount == ea
            result
        }
        
        returnValue.size() == 2
        returnValue == fileInfoList
    }
    
    def "测试exportFilesWithXml方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        
        def xml = "<xml>test</xml>"
        def ea = "test_ea"
        def fileName = "test.txt"
        def token = "test_token"
        def headers = [header1: "value1"]
        
        def result = new NSGetDownloadTokenResult()
        result.token = token
        
        when:
        def returnValue = networkDiskService.exportFilesWithXml(xml, tenantId, ea, user, fileName)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * networkDiskProxy.getDownloadToken(headers, _) >> { args ->
            NSGetDownloadTokenArg arg = args[1]
            assert arg.structure == xml
            assert arg.fileName == fileName
            assert arg.extension == "zip"
            assert arg.employeeId == Integer.parseInt(userId)
            assert arg.enterpriseAccount == ea
            assert arg.operator.enterpriseId == Integer.parseInt(tenantId)
            result
        }
        
        returnValue == token
    }
    
    def "测试packedFile方法 - 基本参数"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getTenantIdInt() >> Integer.parseInt(tenantId)
        user.getUserId() >> userId
        user.getUserIdOrOutUserIdIfOutUser() >> userId
        
        def xml = "<xml>test</xml>"
        def jobId = "job123"
        def ea = "test_ea"
        def expectedResult = Mock(FilePackedResult)
        
        when:
        def returnValue = networkDiskService.packedFile(user, xml, jobId)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * filePackedService.getFilePackedResult(_) >> { args ->
            FilePackedArg arg = args[0]
            assert arg.employId == Integer.parseInt(tenantId)
            assert arg.bizType == "CRM"
            assert arg.ea == ea
            assert arg.documents == xml
            assert arg.downloadUser == userId
            assert arg.downloadSecurityGroup == "XiaoKeNetDisk"
            assert arg.warehouseType == "N"
            assert arg.jobId == jobId
            expectedResult
        }
        
        returnValue == expectedResult
    }
    
    def "测试packedFile方法 - 带totalCount参数"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getTenantIdInt() >> Integer.parseInt(tenantId)
        user.getUserId() >> userId
        user.getUserIdOrOutUserIdIfOutUser() >> userId
        
        def xml = "<xml>test</xml>"
        def jobId = "job123"
        def totalCount = 100
        def ea = "test_ea"
        def expectedResult = Mock(FilePackedResult)
        
        when:
        def returnValue = networkDiskService.packedFile(user, xml, jobId, totalCount)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * filePackedService.getFilePackedResult(_) >> { args ->
            FilePackedArg arg = args[0]
            assert arg.employId == Integer.parseInt(tenantId)
            assert arg.dataCount == totalCount
            expectedResult
        }
        
        returnValue == expectedResult
    }
    
    def "测试packedFile方法 - 带skipDuplicatedFile参数"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getTenantIdInt() >> Integer.parseInt(tenantId)
        user.getUserId() >> userId
        user.getUserIdOrOutUserIdIfOutUser() >> userId
        
        def xml = "<xml>test</xml>"
        def jobId = "job123"
        def totalCount = 100
        def skipDuplicatedFile = true
        def ea = "test_ea"
        def expectedResult = Mock(FilePackedResult)
        
        when:
        def returnValue = networkDiskService.packedFile(user, xml, jobId, totalCount, skipDuplicatedFile)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        1 * filePackedService.getFilePackedResult(_) >> { args ->
            FilePackedArg arg = args[0]
            assert arg.employId == Integer.parseInt(tenantId)
            assert arg.dataCount == totalCount
            assert arg.skipDuplicatedFile == skipDuplicatedFile
            expectedResult
        }
        
        returnValue == expectedResult
    }
    
    def "测试packedFile方法 - XML大小超限制异常"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getTenantIdInt() >> Integer.parseInt(tenantId)
        user.getUserId() >> userId
        user.getUserIdOrOutUserIdIfOutUser() >> userId
        
        // 创建一个大字符串来模拟大XML
        def xml = "<xml>" + ("x" * 10000) + "</xml>"
        def jobId = "job123"
        def ea = "test_ea"
        
        // 设置导出XML大小限制为较小的值，以触发异常
        Whitebox.setInternalState(com.facishare.paas.appframework.common.util.AppFrameworkConfig, "exportXmlMaxLength", 100L)
        
        when:
        networkDiskService.packedFile(user, xml, jobId)
        
        then:
        1 * gdsHandler.getEAByEI(tenantId) >> ea
        thrown(ValidateException)
    }
    
    def "测试getFoldersByParentId方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        user.getUserId() >> userId
        
        def parentId = "parent123"
        def ea = "test_ea"
        def headers = [header1: "value1"]
        
        def expectedResult = Mock(NSGetFoldersByParentIDResult)
        
        when:
        def returnValue = networkDiskService.getFoldersByParentId(parentId, tenantId, ea, user)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * networkDiskProxy.getFoldersByParentId(headers, _) >> { args ->
            NSGetFoldersByParentIDArg arg = args[1]
            assert arg.parentId == parentId
            assert arg.operator.enterpriseId == Integer.parseInt(tenantId)
            assert arg.operator.employeeId == Integer.parseInt(userId)
            assert arg.operator.enterpriseAccount == ea
            expectedResult
        }
        
        returnValue == expectedResult
    }
    
    def "测试getFoldersByParentId方法 - parentId为null"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = Mock(User)
        
        when:
        def returnValue = networkDiskService.getFoldersByParentId(null, tenantId, "ea", user)
        
        then:
        0 * networkDiskProxy.getFoldersByParentId(*_)
        returnValue == null
    }
} 