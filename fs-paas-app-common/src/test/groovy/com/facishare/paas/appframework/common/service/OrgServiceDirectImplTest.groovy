package com.facishare.paas.appframework.common.service

import com.facishare.organization.adapter.api.model.organizationwithouter.OrganizationEmployee
import com.facishare.organization.api.model.department.DepartmentDto
import com.facishare.organization.api.model.employee.EmployeeDto
import com.facishare.organization.api.model.employee.PersonnelNameLanguage
import com.facishare.organization.api.model.type.DepartmentStatus
import com.facishare.organization.api.model.type.EmployeeEntityStatus
import com.facishare.paas.I18N
import com.facishare.paas.appframework.common.service.dto.*
import com.facishare.paas.appframework.core.model.User
import com.facishare.social.department.DepartmentObjService
import com.facishare.social.department.model.BatchGetDepartmentByDeptIds
import com.facishare.social.personnel.model.PersonnelDto
import com.fxiaoke.enterpriserelation2.data.ErDepartmentSimpleData
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class OrgServiceDirectImplTest extends Specification {
    OrgServiceDirectImpl orgService
    OrgServiceProxy orgServiceProxy = Mock(OrgServiceProxy)
    OuterOrganizationService outerOrganizationService = Mock(OuterOrganizationService)
    EmployeeService employeeService = Mock(EmployeeService)
    DepartmentService departmentService = Mock(DepartmentService)
    DepartmentObjService departmentObjService = Mock(DepartmentObjService)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        orgService = new OrgServiceDirectImpl()
        orgService.orgServiceProxy = orgServiceProxy
        orgService.outerOrganizationService = outerOrganizationService
        orgService.employeeService = employeeService
        orgService.departmentService = departmentService
        orgService.departmentObjService = departmentObjService
    }
    
    def "测试getUser方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        
        def employee = new EmployeeDto(
                employeeId: 67890,
                name: "张三",
                nameLanguage: new PersonnelNameLanguage(
                        zhCn: "张三",
                        en: "Zhang San",
                        zhTw: "張三"
                )
        )
        
        when:
        def result = orgService.getUser(tenantId, userId)
        
        then:
        1 * employeeService.getUserInfo(tenantId, userId) >> employee
        
        result.tenantId == tenantId
        result.userId == userId
        result.userName == "张三"
    }
    
    def "测试getUser方法 - 外部用户"() {
        given:
        def tenantId = "12345"
        def userId = "67890123456" // 长ID，外部用户
        
        def outUser = new OrganizationEmployee(
                employeeId: userId,
                name: "李四",
                enterpriseId: tenantId
        )
        
        when:
        def result = orgService.getUser(tenantId, userId)
        
        then:
        1 * outerOrganizationService.batchGetEmployee(tenantId, [userId]) >> [outUser]
        
        result.tenantId == tenantId
        result.userId == userId
        result.userName == "李四"
    }
    
    def "测试getDeptName方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def userIds = ["11111", "22222"]
        
        def deptInfo1 = new QueryDeptInfoByUserIds.MainDeptInfo(
                userId: "11111",
                deptId: "101",
                deptName: "研发部"
        )
        
        def deptInfo2 = new QueryDeptInfoByUserIds.MainDeptInfo(
                userId: "22222",
                deptId: "102",
                deptName: "产品部"
        )
        
        when:
        // 测试getMainDeptInfo方法将在下面的测试用例中完成
        // 这里我们模拟它的返回值
        GroovyMock(OrgServiceDirectImpl, global: true)
        orgService.getMainDeptInfo(tenantId, userId, userIds) >> [
                "11111": deptInfo1,
                "22222": deptInfo2
        ]
        
        def result = orgService.getDeptName(tenantId, userId, userIds)
        
        then:
        result["11111"] == "研发部"
        result["22222"] == "产品部"
    }
    
    def "测试getMembersByDeptIds方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = new User(tenantId, userId)
        def deptIds = ["101", "102"]
        def userStatus = 0 // 启用状态
        
        when:
        def result = orgService.getMembersByDeptIds(user, deptIds, userStatus)
        
        then:
        1 * employeeService.batchGetEmployeeIdsByDeptIds(tenantId, deptIds, userStatus, true) >> ["11111", "22222", "33333"]
        
        result.size() == 3
        result.contains("11111")
        result.contains("22222")
        result.contains("33333")
    }
    
    def "测试getUserNameByIds方法 - 混合内部和外部用户"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def userIds = ["11111", "22222", "333333333333"] // 最后一个是外部用户ID
        
        def innerEmployee1 = new EmployeeDto(
                employeeId: 11111,
                name: "张三",
                mainDepartmentId: 101,
                enterpriseId: 12345
        )
        
        def innerEmployee2 = new EmployeeDto(
                employeeId: 22222,
                name: "李四",
                mainDepartmentId: 102,
                enterpriseId: 12345
        )
        
        def outEmployee = new OrganizationEmployee(
                employeeId: "333333333333",
                name: "王五",
                mainDepartmentId: "103",
                enterpriseId: tenantId
        )
        
        when:
        def result = orgService.getUserNameByIds(tenantId, userId, userIds)
        
        then:
        1 * employeeService.batchGetUserInfo(tenantId, ["11111", "22222"]) >> [innerEmployee1, innerEmployee2]
        1 * outerOrganizationService.batchGetEmployee(tenantId, ["333333333333"]) >> [outEmployee]
        
        result.size() == 3
        result[0].id == "11111"
        result[0].name == "张三"
        result[1].id == "22222"
        result[1].name == "李四"
        result[2].id == "333333333333"
        result[2].name == "王五"
    }
    
    def "测试getUserInfoMapByIds方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def userIds = ["11111", "22222"]
        
        def userInfo1 = new UserInfo(
                id: "11111",
                name: "张三",
                dept: "101"
        )
        
        def userInfo2 = new UserInfo(
                id: "22222",
                name: "李四",
                dept: "102"
        )
        
        when:
        // 使用前面方法的返回值
        GroovyMock(OrgServiceDirectImpl, global: true)
        orgService.getUserNameByIds(tenantId, userId, userIds) >> [userInfo1, userInfo2]
        
        def result = orgService.getUserInfoMapByIds(tenantId, userId, userIds)
        
        then:
        result.size() == 2
        result["11111"] == userInfo1
        result["22222"] == userInfo2
    }
    
    def "测试getDeptInfoNameByIds方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def deptIds = ["101", "102"]
        
        def innerDept = new DepartmentDto(
                departmentId: 101,
                name: "研发部",
                principalId: 11111,
                parentId: 100,
                status: DepartmentStatus.NORMAL
        )
        
        def outDept = new ErDepartmentSimpleData(
                id: "102",
                name: "产品部",
                pid: "100",
                status: 1
        )
        
        when:
        def result = orgService.getDeptInfoNameByIds(tenantId, userId, deptIds)
        
        then:
        1 * departmentService.batchGetDepartment(tenantId, ["101"], QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE) >> [innerDept]
        1 * outerOrganizationService.batchGetOutDepartment(_, ["102"], QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE) >> [outDept]
        
        result.size() == 2
        result[0].deptId == "101"
        result[0].deptName == "研发部"
        result[1].deptId == "102"
        result[1].deptName == "产品部"
    }
    
    def "测试batchGetDeptInfosByDeptIds方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = new User(tenantId, userId)
        def deptIds = ["101", "102"]
        
        def deptDto1 = new com.facishare.social.department.model.DepartmentDto(
                deptId: "101",
                name: "研发部",
                pid: "100"
        )
        
        def deptDto2 = new com.facishare.social.department.model.DepartmentDto(
                deptId: "102",
                name: "产品部",
                pid: "100"
        )
        
        def argument = new BatchGetDepartmentByDeptIds.Argument(
                operatorId: userId,
                tenantId: tenantId,
                deptIds: deptIds
        )
        
        when:
        def result = orgService.batchGetDeptInfosByDeptIds(user, deptIds)
        
        then:
        1 * departmentObjService.getDepartmentByDeptIds(_) >> { args ->
            BatchGetDepartmentByDeptIds.Argument arg = args[0]
            assert arg.operatorId == userId
            assert arg.tenantId == tenantId
            assert arg.deptIds == deptIds
            [deptDto1, deptDto2]
        }
        
        result.size() == 2
        result[0].deptId == "101"
        result[0].deptName == "研发部"
        result[1].deptId == "102"
        result[1].deptName == "产品部"
    }
    
    def "测试getGroupInfoByIds方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def groupIds = ["201", "202"]
        
        def groupInfo1 = new QueryGroupByIds.UserGroupInfo(
                id: "201",
                name: "项目组1",
                description: "项目1小组"
        )
        
        def groupInfo2 = new QueryGroupByIds.UserGroupInfo(
                id: "202",
                name: "项目组2",
                description: "项目2小组"
        )
        
        def result = new QueryGroupByIds.Result(
                success: true,
                result: [groupInfo1, groupInfo2]
        )
        
        when:
        def groups = orgService.getGroupInfoByIds(tenantId, userId, groupIds)
        
        then:
        1 * orgServiceProxy.queryGroupByIds(_) >> { args ->
            QueryGroupByIds.Arg arg = args[0]
            assert arg.tenantId == tenantId
            assert arg.userId == userId
            assert arg.groupIdList == groupIds
            result
        }
        
        groups.size() == 2
        groups[0].id == "201"
        groups[0].name == "项目组1"
        groups[1].id == "202"
        groups[1].name == "项目组2"
    }
    
    def "测试batchGetOutUsers方法"() {
        given:
        def tenantId = "12345"
        def outUserIds = ["111111111111", "222222222222"] as Set
        
        def outUser1 = new OrganizationEmployee(
                employeeId: "111111111111",
                name: "外部用户1",
                enterpriseId: "98765",
                status: EmployeeEntityStatus.NORMAL.name(),
                profileImage: "http://example.com/avatar1.jpg"
        )
        
        def outUser2 = new OrganizationEmployee(
                employeeId: "222222222222",
                name: "外部用户2",
                enterpriseId: "98765",
                status: EmployeeEntityStatus.NORMAL.name(),
                profileImage: "http://example.com/avatar2.jpg"
        )
        
        when:
        def result = orgService.batchGetOutUsers(tenantId, outUserIds)
        
        then:
        1 * outerOrganizationService.batchGetEmployee(tenantId, outUserIds) >> [outUser1, outUser2]
        
        result.size() == 2
        result[0].id == "111111111111"
        result[0].name == "外部用户1"
        result[0].profile == "http://example.com/avatar1.jpg"
        result[0].status == 0
        result[1].id == "222222222222"
        result[1].name == "外部用户2"
    }
    
    def "测试getDeptByName方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def names = ["研发部", "产品部"]
        
        def dept1 = new DepartmentDto(
                departmentId: 101,
                name: "研发部",
                principalId: 11111,
                parentId: 100,
                status: DepartmentStatus.NORMAL,
                ancestors: [1, 100] as List<Integer>,
                departmentOrder: 1,
                enterpriseId: 12345,
                recordType: DepartmentDto.RECORD_TYPE_DEPARTMENT
        )
        
        def dept2 = new DepartmentDto(
                departmentId: 102,
                name: "产品部",
                principalId: 22222,
                parentId: 100,
                status: DepartmentStatus.NORMAL,
                ancestors: [1, 100] as List<Integer>,
                departmentOrder: 2,
                enterpriseId: 12345,
                recordType: DepartmentDto.RECORD_TYPE_DEPARTMENT
        )
        
        when:
        def result = orgService.getDeptByName(tenantId, userId, names)
        
        then:
        1 * departmentService.batchGetDepartmentByNames(tenantId, names, QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE) >> [dept1, dept2]
        
        result.size() == 2
        result[0].id == "101"
        result[0].name == "研发部"
        result[0].managerId == "11111"
        result[0].parentId == "100"
        result[0].ancestors == ["1", "100"]
        result[0].order == 1
        result[0].deptType == QueryDeptByName.TYPE_DEPT
        
        result[1].id == "102"
        result[1].name == "产品部"
    }
    
    def "测试getUserByName方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def names = ["张三", "李四"]
        def userStatus = 0
        
        def employee1 = new EmployeeDto(
                employeeId: 11111,
                name: "张三",
                mainDepartmentId: 101,
                enterpriseId: 12345,
                email: "<EMAIL>",
                telephone: "13800138001",
                mobile: "13800138001",
                post: "开发工程师",
                status: EmployeeEntityStatus.NORMAL
        )
        
        def employee2 = new EmployeeDto(
                employeeId: 22222,
                name: "李四",
                mainDepartmentId: 102,
                enterpriseId: 12345,
                email: "<EMAIL>",
                telephone: "13800138002",
                mobile: "13800138002",
                post: "产品经理",
                status: EmployeeEntityStatus.NORMAL
        )
        
        when:
        def result = orgService.getUserByName(tenantId, userId, names, userStatus)
        
        then:
        1 * employeeService.batchGetUserByNickNames(tenantId, names, userStatus) >> [employee1, employee2]
        
        result.size() == 2
        result[0].id == "11111"
        result[0].name == "张三"
        result[0].email == "<EMAIL>"
        result[0].phone == "13800138001"
        result[0].mobile == "13800138001"
        result[0].post == "开发工程师"
        result[0].dept == "101"
        result[0].status == 0
        
        result[1].id == "22222"
        result[1].name == "李四"
    }
    
    def "测试getUserByCodes方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = new User(tenantId, userId)
        def codes = ["001", "002"]
        
        def personnel1 = new PersonnelDto(
                userId: "11111",
                name: "张三",
                mainDepartment: "101",
                tenantId: tenantId,
                email: "<EMAIL>",
                telephone: "13800138001",
                post: "开发工程师",
                status: "0",
                empNum: "001"
        )
        
        def personnel2 = new PersonnelDto(
                userId: "22222",
                name: "李四",
                mainDepartment: "102",
                tenantId: tenantId,
                email: "<EMAIL>",
                telephone: "13800138002",
                post: "产品经理",
                status: "0",
                empNum: "002"
        )
        
        when:
        def result = orgService.getUserByCodes(user, codes)
        
        then:
        1 * employeeService.batchGetUserByCodes(user, codes) >> [personnel1, personnel2]
        
        result.size() == 2
        result[0].id == "11111"
        result[0].name == "张三"
        result[0].email == "<EMAIL>"
        result[0].phone == "13800138001"
        result[0].dept == "101"
        result[0].empNum == "001"
        
        result[1].id == "22222"
        result[1].name == "李四"
        result[1].empNum == "002"
    }
    
    def "测试batchGetOutDeptInfosByDeptIds方法"() {
        given:
        def tenantId = "12345"
        def userId = "67890"
        def user = new User(tenantId, userId)
        def deptIds = ["101", "102"]
        def status = QueryDeptInfoByDeptIds.DeptStatusEnum.ENABLE
        
        def outDept1 = new ErDepartmentSimpleData(
                id: "101",
                name: "外部部门1",
                pid: "100",
                status: 1
        )
        
        def outDept2 = new ErDepartmentSimpleData(
                id: "102",
                name: "外部部门2",
                pid: "100",
                status: 0
        )
        
        when:
        def result = orgService.batchGetOutDeptInfosByDeptIds(user, deptIds, status)
        
        then:
        1 * outerOrganizationService.batchGetOutDepartment(user, deptIds, status) >> [outDept1, outDept2]
        
        result.size() == 2
        result[0].deptId == "101"
        result[0].deptName == "外部部门1"
        result[0].parentId == "100"
        result[0].status == 1
        
        result[1].deptId == "102"
        result[1].deptName == "外部部门2"
        result[1].parentId == "100"
        result[1].status == 0
    }
    
    def "测试private方法getEmployeeName"() {
        given:
        def employee = new EmployeeDto(
                name: "张三",
                nameLanguage: nameLanguage
        )
        
        when:
        def result = Whitebox.invokeMethod(orgService, "getEmployeeName", employee)
        
        then:
        result == expectedName
        
        where:
        nameLanguage                                                  | expectedName
        null                                                          | "张三"
        new PersonnelNameLanguage(zhCn: "张三", en: "Zhang San")      | "张三"
        new PersonnelNameLanguage(zhCn: "", en: "Zhang San")          | "张三"
        new PersonnelNameLanguage(zhCn: "张三", en: "")               | "张三"
    }
} 