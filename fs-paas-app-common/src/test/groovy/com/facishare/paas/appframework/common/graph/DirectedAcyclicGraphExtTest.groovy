package com.facishare.paas.appframework.common.graph

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：DirectedAcyclicGraphExt有向无环图扩展工具类的单元测试
 */
class DirectedAcyclicGraphExtTest extends Specification {

    def "test isEmpty with null graph"() {
        when: "检查null图是否为空"
        def result = DirectedAcyclicGraphExt.isEmpty(null)

        then: "应该返回true"
        result == true
    }

    def "test isEmpty with empty graph"() {
        given: "创建空图"
        def graph = GraphBuilder.directed().build()

        when: "检查空图是否为空"
        def result = DirectedAcyclicGraphExt.isEmpty(graph)

        then: "应该返回true"
        result == true
    }

    def "test isEmpty with non-empty graph"() {
        given: "创建非空图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "检查非空图是否为空"
        def result = DirectedAcyclicGraphExt.isEmpty(graph)

        then: "应该返回false"
        result == false
    }

    def "test notEmpty with null graph"() {
        when: "检查null图是否非空"
        def result = DirectedAcyclicGraphExt.notEmpty(null)

        then: "应该返回false"
        result == false
    }

    def "test notEmpty with empty graph"() {
        given: "创建空图"
        def graph = GraphBuilder.directed().build()

        when: "检查空图是否非空"
        def result = DirectedAcyclicGraphExt.notEmpty(graph)

        then: "应该返回false"
        result == false
    }

    def "test notEmpty with non-empty graph"() {
        given: "创建非空图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "检查非空图是否非空"
        def result = DirectedAcyclicGraphExt.notEmpty(graph)

        then: "应该返回true"
        result == true
    }

    def "test isEmpty and notEmpty are complementary"() {
        given: "创建各种图"
        def nullGraph = null
        def emptyGraph = GraphBuilder.directed().build()
        def singleNodeGraph = GraphBuilder.directed().build()
        singleNodeGraph.addNode("A")
        def multiNodeGraph = GraphBuilder.directed().build()
        multiNodeGraph.addNode("A")
        multiNodeGraph.addNode("B")
        multiNodeGraph.putEdge("A", "B")

        expect: "isEmpty和notEmpty应该是互补的"
        DirectedAcyclicGraphExt.isEmpty(nullGraph) == !DirectedAcyclicGraphExt.notEmpty(nullGraph)
        DirectedAcyclicGraphExt.isEmpty(emptyGraph) == !DirectedAcyclicGraphExt.notEmpty(emptyGraph)
        DirectedAcyclicGraphExt.isEmpty(singleNodeGraph) == !DirectedAcyclicGraphExt.notEmpty(singleNodeGraph)
        DirectedAcyclicGraphExt.isEmpty(multiNodeGraph) == !DirectedAcyclicGraphExt.notEmpty(multiNodeGraph)
    }

    def "test with different graph types"() {
        given: "创建不同类型的图"
        def directedGraph = GraphBuilder.directed().build()
        directedGraph.addNode("A")
        
        def undirectedGraph = GraphBuilder.undirected().build()
        undirectedGraph.addNode("B")
        
        def valueGraph = ValueGraphBuilder.directed().build()
        valueGraph.addNode("C")

        expect: "所有非空图都应该被正确识别"
        !DirectedAcyclicGraphExt.isEmpty(directedGraph)
        DirectedAcyclicGraphExt.notEmpty(directedGraph)
        
        !DirectedAcyclicGraphExt.isEmpty(undirectedGraph)
        DirectedAcyclicGraphExt.notEmpty(undirectedGraph)
        
        !DirectedAcyclicGraphExt.isEmpty(valueGraph)
        DirectedAcyclicGraphExt.notEmpty(valueGraph)
    }

    def "test with graphs containing only edges"() {
        given: "创建只有边没有孤立节点的图"
        def graph = GraphBuilder.directed().build()
        graph.putEdge("A", "B") // 这会自动添加节点A和B

        when: "检查图是否为空"
        def isEmpty = DirectedAcyclicGraphExt.isEmpty(graph)
        def notEmpty = DirectedAcyclicGraphExt.notEmpty(graph)

        then: "图应该被识别为非空"
        !isEmpty
        notEmpty
        graph.nodes().size() == 2
    }

    def "test with self-loop graphs"() {
        given: "创建有自环的图"
        def graph = GraphBuilder.directed().allowsSelfLoops(true).build()
        graph.putEdge("A", "A") // 自环

        when: "检查图是否为空"
        def isEmpty = DirectedAcyclicGraphExt.isEmpty(graph)
        def notEmpty = DirectedAcyclicGraphExt.notEmpty(graph)

        then: "图应该被识别为非空"
        !isEmpty
        notEmpty
        graph.nodes().size() == 1
    }

    def "test with large graphs"() {
        given: "创建大图"
        def largeGraph = GraphBuilder.directed().build()
        (1..1000).each { i ->
            largeGraph.addNode("Node$i")
        }

        when: "检查大图是否为空"
        def isEmpty = DirectedAcyclicGraphExt.isEmpty(largeGraph)
        def notEmpty = DirectedAcyclicGraphExt.notEmpty(largeGraph)

        then: "大图应该被识别为非空"
        !isEmpty
        notEmpty
        largeGraph.nodes().size() == 1000
    }

    def "test constants"() {
        expect: "验证常量值"
        DirectedAcyclicGraphExt.GRAPH_HAS_CYCLE == "graph %s has cycle."
    }

    def "test method behavior consistency"() {
        given: "创建测试图集合"
        def testGraphs = [
            null,
            GraphBuilder.directed().build(),
            GraphBuilder.undirected().build(),
            ValueGraphBuilder.directed().build()
        ]
        
        // 添加一些节点到非null图
        testGraphs[1].addNode("A")
        testGraphs[2].addNode("B")
        testGraphs[3].addNode("C")

        expect: "方法行为应该一致"
        testGraphs.each { graph ->
            def isEmpty = DirectedAcyclicGraphExt.isEmpty(graph)
            def notEmpty = DirectedAcyclicGraphExt.notEmpty(graph)
            
            // isEmpty和notEmpty应该是互补的
            assert isEmpty == !notEmpty
            
            // 对于null图，isEmpty应该为true，notEmpty应该为false
            if (graph == null) {
                assert isEmpty == true
                assert notEmpty == false
            } else {
                // 对于有节点的图，isEmpty应该为false，notEmpty应该为true
                if (graph.nodes().size() > 0) {
                    assert isEmpty == false
                    assert notEmpty == true
                } else {
                    // 对于空图，isEmpty应该为true，notEmpty应该为false
                    assert isEmpty == true
                    assert notEmpty == false
                }
            }
        }
    }

    def "test edge cases with different node types"() {
        given: "创建包含不同类型节点的图"
        def stringGraph = GraphBuilder.directed().build()
        stringGraph.addNode("string_node")
        
        def integerGraph = GraphBuilder.directed().build()
        integerGraph.addNode(42)
        
        def objectGraph = GraphBuilder.directed().build()
        objectGraph.addNode([id: 1, name: "object_node"])

        expect: "所有类型的节点都应该被正确处理"
        DirectedAcyclicGraphExt.notEmpty(stringGraph)
        DirectedAcyclicGraphExt.notEmpty(integerGraph)
        DirectedAcyclicGraphExt.notEmpty(objectGraph)
        
        !DirectedAcyclicGraphExt.isEmpty(stringGraph)
        !DirectedAcyclicGraphExt.isEmpty(integerGraph)
        !DirectedAcyclicGraphExt.isEmpty(objectGraph)
    }

    def "test performance with repeated calls"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "多次调用方法"
        def results = []
        (1..1000).each {
            results << DirectedAcyclicGraphExt.isEmpty(graph)
            results << DirectedAcyclicGraphExt.notEmpty(graph)
        }

        then: "所有结果应该一致"
        results.every { it instanceof Boolean }
        results.findAll { it == false }.size() == 1000 // isEmpty的结果
        results.findAll { it == true }.size() == 1000  // notEmpty的结果
    }

    def "test thread safety"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "并发调用方法"
        def results = []
        def threads = (1..10).collect { i ->
            Thread.start {
                (1..100).each {
                    synchronized(results) {
                        results << DirectedAcyclicGraphExt.isEmpty(graph)
                        results << DirectedAcyclicGraphExt.notEmpty(graph)
                    }
                }
            }
        }
        threads.each { it.join() }

        then: "所有结果应该一致"
        results.size() == 2000
        results.findAll { it == false }.size() == 1000 // isEmpty的结果
        results.findAll { it == true }.size() == 1000  // notEmpty的结果
    }
}
