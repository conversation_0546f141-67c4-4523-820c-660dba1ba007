package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.SearchData
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class DataSearchServiceImplTest extends Specification {
    
    DataSearchServiceImpl dataSearchService
    ObjectSearchServiceProxy objectSearchServiceProxy = Mock(ObjectSearchServiceProxy)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        dataSearchService = new DataSearchServiceImpl()
        dataSearchService.objectSearchServiceProxy = objectSearchServiceProxy
    }
    
    def "测试searchData方法 - 正常场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        user.getUserId() >> "67890"
        
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        def apiIdsMap = ["AccountObj": ["1", "2", "3"], "ContactObj": ["4", "5"]]
        
        def arg = new SearchData.Arg()
        arg.setQuery("测试查询")
        arg.setAccurateQuery(true)
        
        def result = SearchData.Result.builder()
                .totalSize(5)
                .apiIdsMap(apiIdsMap)
                .build()
        
        when:
        def actualResult = dataSearchService.searchData(arg, user)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * objectSearchServiceProxy.searchData(headers, _ as SearchData.Arg) >> { h, a ->
            assert a.query == "测试查询"
            assert a.accurateQuery == true
            assert a.includeNameResults == false
            assert a.includeApiIdsMap == true
            return result
        }
        
        actualResult == result
        actualResult.totalSize == 5
        actualResult.apiIdsMap == apiIdsMap
    }
    
    def "测试searchData方法 - 参数为null场景"() {
        given:
        def user = Mock(User)
        
        when:
        def result = dataSearchService.searchData(null, user)
        
        then:
        0 * objectSearchServiceProxy.searchData(_, _)
        
        result != null
        result.totalSize == 0
        result.apiIdsMap != null
        result.apiIdsMap.isEmpty()
    }
    
    def "测试searchData方法 - 服务返回null场景"() {
        given:
        def user = Mock(User)
        def arg = new SearchData.Arg()
        
        when:
        def result = dataSearchService.searchData(arg, user)
        
        then:
        1 * RestUtils.buildHeaders(user) >> [:]
        1 * objectSearchServiceProxy.searchData(_, arg) >> null
        
        result != null
        result.totalSize == 0
        result.apiIdsMap != null
        result.apiIdsMap.isEmpty()
    }
    
    def "测试searchDataByName方法 - 正常场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        user.getUserId() >> "67890"
        
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "67890"]
        
        def arg = new SearchData.Arg()
        arg.setQuery("测试查询")
        
        // 创建搜索结果的内部类对象
        def nameResults = [
            [apiName: "AccountObj", name: "客户1", id: "1001"],
            [apiName: "ContactObj", name: "联系人1", id: "2001"]
        ]
        
        def result = new SearchDataByName.Result(totalSize: 2, nameResults: nameResults)
        
        when:
        def actualResult = dataSearchService.searchDataByName(arg, user)
        
        then:
        1 * RestUtils.buildHeaders(user) >> headers
        1 * objectSearchServiceProxy.searchDataByName(headers, arg) >> result
        
        actualResult == result
        actualResult.totalSize == 2
        actualResult.nameResults.size() == 2
        actualResult.nameResults[0].apiName == "AccountObj"
        actualResult.nameResults[1].apiName == "ContactObj"
    }
    
    def "测试searchDataByName方法 - 服务返回null场景"() {
        given:
        def user = Mock(User)
        def arg = new SearchData.Arg()
        
        when:
        def result = dataSearchService.searchDataByName(arg, user)
        
        then:
        1 * RestUtils.buildHeaders(user) >> [:]
        1 * objectSearchServiceProxy.searchDataByName(_, arg) >> null
        
        result != null
        result.totalSize == 0
        result.nameResults != null
        result.nameResults.isEmpty()
    }
} 