package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.FindObjectPageComponentList
import com.facishare.paas.appframework.common.util.WebPageConfig
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.fxiaoke.release.FsGrayReleaseBiz
import org.powermock.reflect.Whitebox
import spock.lang.Specification

import java.lang.reflect.Field

class WebPageProxyServiceTest extends Specification {
    
    WebPageProxyService webPageProxyService
    WebPageProxy webPageProxy = Mock(WebPageProxy)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
        }
    
    def setup() {
        webPageProxyService = new WebPageProxyService(webPageProxy)
    }
    
    def "测试findCustomerWidget无参方法"() {
        given:
        def components = [
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp1"),
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp2")
        ]
        
        when:
        // 模拟WebPageConfig.findAllObjectPageComponentList方法返回
        Whitebox.setInternalState(WebPageConfig, "allObjectPageComponentList", components)
        def result = webPageProxyService.findCustomerWidget()
        
        then:
        result.size() == 2
        result[0].apiName == "comp1"
        result[1].apiName == "comp2"
    }
    
    def "测试findCustomerWidget有参方法 - 使用本地配置"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        
        def bizId = "test_biz"
        def templeType = "test_type"
        
        def components = [
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp1"),
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp2")
        ]
        
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
//        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier
        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        // 获取 static final 字段
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        
        when:
        // 模拟配置项打开
        fsGrayReleaseBiz.isAllow(*_) >> true
        
        // 模拟本地配置返回
        GroovyMock(WebPageConfig, global: true)
        WebPageConfig.findObjectPageComponentList(bizId, templeType) >> components
        
        def result = webPageProxyService.findCustomerWidget(user, bizId, templeType)
        
        then:
        result.size() == 2
        result[0].apiName == "comp1"
        result[1].apiName == "comp2"
        0 * webPageProxy.findObjectPageComponentList(*_) // 确认没有调用远程接口
    }
    
    def "测试findCustomerWidget有参方法 - 调用远程服务"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        
        def bizId = "test_biz"
        def templeType = "test_type"
        
        def headers = [header1: "value1"]
        
        def components = [
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp1"),
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp2")
        ]
        
        def result = new FindObjectPageComponentList.Result()
        result.objectPageComponents = components
        
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        
        when:
        // 模拟配置项关闭
        fsGrayReleaseBiz.isAllow(*_) >> false
        
        // 模拟调用远程服务
        RestUtils.buildHeaders(user) >> headers
        webPageProxy.findObjectPageComponentList(headers, _) >> { args ->
            FindObjectPageComponentList.Arg arg = args[1]
            assert arg.bizId == bizId
            assert arg.templeType == templeType
            result
        }
        
        def returnValue = webPageProxyService.findCustomerWidget(user, bizId, templeType)
        
        then:
        returnValue.size() == 2
        returnValue[0].apiName == "comp1"
        returnValue[1].apiName == "comp2"
    }
    
    def "测试findCustomerWidget有参方法 - 远程服务调用异常"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        
        def bizId = "test_biz"
        def templeType = "test_type"
        
        def headers = [header1: "value1"]
        
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        
        when:
        // 模拟配置项关闭
        fsGrayReleaseBiz.isAllow(*_) >> false
        
        // 模拟调用远程服务抛出异常
        RestUtils.buildHeaders(user) >> headers
        webPageProxy.findObjectPageComponentList(headers, _) >> { throw new RuntimeException("远程调用失败") }
        
        def returnValue = webPageProxyService.findCustomerWidget(user, bizId, templeType)
        
        then:
        // 应该返回空列表而不是抛出异常
        returnValue.isEmpty()
    }
    
    def "测试缓存功能"() {
        given:
        def tenantId = "12345"
        def user = Mock(User)
        user.getTenantId() >> tenantId
        
        def bizId = "test_biz"
        def templeType = "test_type"
        
        def headers = [header1: "value1"]
        
        def components = [
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp1"),
            new FindObjectPageComponentList.ObjectPageComponent(apiName: "comp2")
        ]
        
        def result = new FindObjectPageComponentList.Result()
        result.objectPageComponents = components
        
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        
        when:
        // 模拟配置项关闭
        fsGrayReleaseBiz.isAllow(*_) >> false
        
        // 模拟调用远程服务
        RestUtils.buildHeaders(user) >> headers
        webPageProxy.findObjectPageComponentList(headers, _) >> result
        
        // 第一次调用
        def firstCall = webPageProxyService.findCustomerWidget(user, bizId, templeType)
        
        // 第二次调用（应该从缓存中获取）
        def secondCall = webPageProxyService.findCustomerWidget(user, bizId, templeType)
        
        then:
        firstCall.size() == 2
        secondCall.size() == 2
        firstCall == secondCall
        
        // 验证远程服务只被调用了一次
        1 * webPageProxy.findObjectPageComponentList(_, _)
    }
} 