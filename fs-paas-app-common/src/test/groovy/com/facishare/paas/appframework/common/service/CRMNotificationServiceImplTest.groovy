package com.facishare.paas.appframework.common.service

import com.facishare.paas.appframework.common.service.dto.SendCrmMessageModel
import com.facishare.paas.appframework.common.service.dto.SendNewCrmMessageModel
import com.facishare.paas.appframework.common.service.model.CRMNotification
import com.facishare.paas.appframework.common.service.model.NewCrmNotification
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.core.util.RestUtils
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class CRMNotificationServiceImplTest extends Specification {
    
    CRMNotificationServiceImpl crmNotificationService
    SendCrmMessageProxy sendCrmMessageProxy = Mock(SendCrmMessageProxy)
    SendNewCrmMessageProxy sendNewCrmMessageProxy = Mock(SendNewCrmMessageProxy)
    MessagePlatformService messagePlatformService = Mock(MessagePlatformService)
    
    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }
    
    def setup() {
        crmNotificationService = new CRMNotificationServiceImpl()
        crmNotificationService.sendCrmMessageProxy = sendCrmMessageProxy
        crmNotificationService.sendNewCrmMessageProxy = sendNewCrmMessageProxy
        crmNotificationService.messagePlatformService = messagePlatformService
        
        // 设置静态变量
        Whitebox.setInternalState(CRMNotificationServiceImpl, "SUPPORT_CRM_REMIND", true)
        Whitebox.setInternalState(CRMNotificationServiceImpl, "CLOSE_OLD_CRM_REMIND", false)
    }
    
    def "测试sendCRMNotification方法 - 老通知关闭场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        
        def notification = new CRMNotification()
        notification.setSender("1001")
        notification.setContent("测试内容")
        notification.setTitle("测试标题")
        notification.setRemindRecordType(1)
        notification.setContent2Id("contentId")
        notification.setDataId("dataId")
        notification.setReceiverIds(["2001", "2002"] as Set)
        notification.setFixContent2ID(true)
        notification.setOutEI("67890")
        notification.setAppId("appId123")
        
        def result = new SendCrmMessageModel.Result()
        result.setSuccess(true)
        
        // 设置老通知开启
        Whitebox.setInternalState(CRMNotificationServiceImpl, "CLOSE_OLD_CRM_REMIND", true)
        
        when:
        crmNotificationService.sendCRMNotification(user, notification)
        
        then:
        1 * sendCrmMessageProxy.sendCrmMessages(user.getTenantId(), _ as SendCrmMessageModel.Arg) >> { tenantId, arg ->
            assert tenantId == "12345"
            assert arg.employeeId == "1001"
            assert arg.content == "测试内容"
            assert arg.title == "测试标题"
            assert arg.remindRecordType == 1
            assert arg.content2Id == "contentId"
            assert arg.dataId == "dataId"
            assert arg.receiverIds == ["2001", "2002"] as Set
            assert arg.fixContent2ID == true
            assert arg.outEI == "67890"
            assert arg.appId == "appId123"
            return result
        }
        
        1 * messagePlatformService.sendTextMessage(user, notification)
        0 * _
    }
    
    def "测试sendCRMNotification方法 - 老通知关闭但接口返回失败"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        
        def notification = new CRMNotification()
        notification.setSender("1001")
        notification.setContent("测试内容")
        
        def result = new SendCrmMessageModel.Result()
        result.setSuccess(false)
        
        // 设置老通知开启
        Whitebox.setInternalState(CRMNotificationServiceImpl, "CLOSE_OLD_CRM_REMIND", true)
        
        when:
        crmNotificationService.sendCRMNotification(user, notification)
        
        then:
        1 * sendCrmMessageProxy.sendCrmMessages(user.getTenantId(), _ as SendCrmMessageModel.Arg) >> result
        1 * messagePlatformService.sendTextMessage(user, notification)
        0 * _
    }
    
    def "测试sendCRMNotification方法 - 消息平台异常场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        
        def notification = new CRMNotification()
        notification.setSender("1001")
        
        // 设置老通知关闭
        Whitebox.setInternalState(CRMNotificationServiceImpl, "CLOSE_OLD_CRM_REMIND", false)
        
        when:
        crmNotificationService.sendCRMNotification(user, notification)
        
        then:
        1 * messagePlatformService.sendTextMessage(user, notification) >> { throw new RuntimeException("消息平台异常") }
        0 * sendCrmMessageProxy._
        notThrown(Exception) // 确保异常被捕获处理
    }
    
    def "测试sendNewCrmNotification方法 - 正常场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        user.getTenantIdInt() >> 12345
        
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "1001"]
        RestUtils.metaClass.static.buildHeaders = { User u -> headers }
        
        def notification = new NewCrmNotification()
        notification.setSourceId("source123")
        notification.setSenderId("1001")
        notification.setType(1)
        notification.setReceiverIDs(["2001", "2002"] as Set)
        notification.setTitle("新消息标题")
        
        def result = new SendNewCrmMessageModel.Result()
        result.setSuccess(true)
        
        when:
        crmNotificationService.sendNewCrmNotification(user, notification)
        
        then:
        1 * sendNewCrmMessageProxy.sendNewCrmMessages(headers, _ as SendNewCrmMessageModel.RemindRecordItem) >> { h, item ->
            assert item.ei == 12345
            assert item.uuid == "source123"
            assert item.remindRecordItem.senderId == "1001"
            assert item.remindRecordItem.type == 1
            assert item.remindRecordItem.receiverIDs == ["2001", "2002"] as Set
            assert item.remindRecordItem.title == "新消息标题"
            return result
        }
        
        1 * messagePlatformService.sendPlatFormMessage(user, notification)
        0 * _
    }
    
    def "测试sendNewCrmNotification方法 - CRM提醒关闭场景"() {
        given:
        def user = Mock(User)
        def notification = new NewCrmNotification()
        
        // 关闭CRM提醒
        Whitebox.setInternalState(CRMNotificationServiceImpl, "SUPPORT_CRM_REMIND", false)
        
        when:
        crmNotificationService.sendNewCrmNotification(user, notification)
        
        then:
        0 * sendNewCrmMessageProxy._
        1 * messagePlatformService.sendPlatFormMessage(user, notification)
        0 * _
    }
    
    def "测试sendNewCrmNotification方法 - 消息平台异常场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        user.getTenantIdInt() >> 12345
        
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "1001"]
        RestUtils.metaClass.static.buildHeaders = { User u -> headers }
        
        def notification = new NewCrmNotification()
        notification.setSourceId("source123")
        
        def result = new SendNewCrmMessageModel.Result()
        result.setSuccess(true)
        
        // 打开CRM提醒
        Whitebox.setInternalState(CRMNotificationServiceImpl, "SUPPORT_CRM_REMIND", true)
        
        when:
        crmNotificationService.sendNewCrmNotification(user, notification)
        
        then:
        1 * sendNewCrmMessageProxy.sendNewCrmMessages(headers, _ as SendNewCrmMessageModel.RemindRecordItem) >> result
        1 * messagePlatformService.sendPlatFormMessage(user, notification) >> { throw new RuntimeException("消息平台异常") }
        0 * _
        notThrown(Exception) // 确保异常被捕获处理
    }
    
    def "测试sendNewCrmNotification方法 - 消息发送失败场景"() {
        given:
        def user = Mock(User)
        user.getTenantId() >> "12345"
        user.getTenantIdInt() >> 12345
        
        def headers = ["X-FS-TENANT-ID": "12345", "X-FS-USER-ID": "1001"]
        RestUtils.metaClass.static.buildHeaders = { User u -> headers }
        
        def notification = new NewCrmNotification()
        notification.setSourceId("source123")
        
        def result = new SendNewCrmMessageModel.Result()
        result.setSuccess(false)
        
        // 打开CRM提醒
        Whitebox.setInternalState(CRMNotificationServiceImpl, "SUPPORT_CRM_REMIND", true)
        
        when:
        crmNotificationService.sendNewCrmNotification(user, notification)
        
        then:
        1 * sendNewCrmMessageProxy.sendNewCrmMessages(headers, _ as SendNewCrmMessageModel.RemindRecordItem) >> result
        1 * messagePlatformService.sendPlatFormMessage(user, notification)
        0 * _
    }
    
    def cleanup() {
        // 清理元类修改
        RestUtils.metaClass = null
    }
} 