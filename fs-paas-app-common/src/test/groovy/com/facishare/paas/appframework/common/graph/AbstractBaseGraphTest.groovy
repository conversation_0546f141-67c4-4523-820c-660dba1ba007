package com.facishare.paas.appframework.common.graph

import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.ImmutableSet
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：AbstractBaseGraph类的单元测试
 */
class AbstractBaseGraphTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    // 创建AbstractBaseGraph的具体实现用于测试
    class TestGraph<N> extends AbstractBaseGraph<N> {
        private final boolean directed
        private final boolean allowsSelfLoops
        private final Map<N, Set<N>> successorMap = [:]
        private final Map<N, Set<N>> predecessorMap = [:]
        private ElementOrder<N> elementOrder = ElementOrder.insertion()

        TestGraph(boolean directed, boolean allowsSelfLoops) {
            this.directed = directed
            this.allowsSelfLoops = allowsSelfLoops
        }

        @Override
        Set<N> nodes() {
            return ImmutableSet.copyOf(successorMap.keySet())
        }

        @Override
        boolean isDirected() {
            return directed
        }

        @Override
        boolean allowsSelfLoops() {
            return allowsSelfLoops
        }

        @Override
        ElementOrder<N> nodeOrder() {
            return elementOrder
        }

        @Override
        Set<N> adjacentNodes(N node) {
            if (!successorMap.containsKey(node)) {
                throw new IllegalArgumentException("Node $node is not an element of this graph")
            }
            if (directed) {
                return ImmutableSet.builder()
                        .addAll(predecessorMap.getOrDefault(node, [] as Set))
                        .addAll(successorMap.getOrDefault(node, [] as Set))
                        .build()
            } else {
                return ImmutableSet.copyOf(successorMap.getOrDefault(node, [] as Set))
            }
        }

        @Override
        Set<N> predecessors(N node) {
            if (!successorMap.containsKey(node)) {
                throw new IllegalArgumentException("Node $node is not an element of this graph")
            }
            if (directed) {
                return ImmutableSet.copyOf(predecessorMap.getOrDefault(node, [] as Set))
            } else {
                return ImmutableSet.copyOf(successorMap.getOrDefault(node, [] as Set))
            }
        }

        @Override
        Set<N> successors(N node) {
            if (!successorMap.containsKey(node)) {
                throw new IllegalArgumentException("Node $node is not an element of this graph")
            }
            return ImmutableSet.copyOf(successorMap.getOrDefault(node, [] as Set))
        }

        // 帮助方法，用于测试
        void addNode(N node) {
            successorMap.putIfAbsent(node, [] as Set)
            if (directed) {
                predecessorMap.putIfAbsent(node, [] as Set)
            }
        }

        void addEdge(N source, N target) {
            addNode(source)
            addNode(target)
            successorMap.get(source).add(target)
            if (directed) {
                predecessorMap.get(target).add(source)
            } else {
                successorMap.get(target).add(source)
            }
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试edgeCount方法计算图中的边数
     */
    def "测试edgeCount方法计算图中的边数"() {
        given: "创建有向图和无向图"
        def directedGraph = new TestGraph<String>(true, false)
        def undirectedGraph = new TestGraph<String>(false, false)
        
        when: "添加节点和边到有向图"
        directedGraph.addNode("A")
        directedGraph.addNode("B")
        directedGraph.addNode("C")
        directedGraph.addEdge("A", "B")
        directedGraph.addEdge("B", "C")
        directedGraph.addEdge("A", "C")
        
        then: "edgeCount应返回正确的边数"
        Whitebox.invokeMethod(directedGraph, "edgeCount") == 3
        
        when: "添加节点和边到无向图"
        undirectedGraph.addNode("A")
        undirectedGraph.addNode("B")
        undirectedGraph.addNode("C")
        undirectedGraph.addEdge("A", "B")
        undirectedGraph.addEdge("B", "C")
        
        then: "edgeCount应返回正确的边数"
        Whitebox.invokeMethod(undirectedGraph, "edgeCount") == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试edges方法返回图中的所有边
     */
    def "测试edges方法返回图中的所有边"() {
        given: "创建有向图和无向图"
        def directedGraph = new TestGraph<String>(true, false)
        def undirectedGraph = new TestGraph<String>(false, false)
        
        when: "添加节点和边到有向图"
        directedGraph.addNode("A")
        directedGraph.addNode("B")
        directedGraph.addEdge("A", "B")
        
        then: "edges应返回正确的边集合"
        def directedEdges = directedGraph.edges()
        directedEdges.size() == 1
        directedEdges.contains(EndpointPair.ordered("A", "B"))
        
        when: "添加节点和边到无向图"
        undirectedGraph.addNode("A")
        undirectedGraph.addNode("B")
        undirectedGraph.addEdge("A", "B")
        
        then: "edges应返回正确的边集合"
        def undirectedEdges = undirectedGraph.edges()
        undirectedEdges.size() == 1
        undirectedEdges.contains(EndpointPair.unordered("A", "B"))
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试incidentEdges方法返回与节点相关的边
     */
    def "测试incidentEdges方法返回与节点相关的边"() {
        given: "创建有向图"
        def directedGraph = new TestGraph<String>(true, false)
        
        when: "添加节点和边到有向图"
        directedGraph.addNode("A")
        directedGraph.addNode("B")
        directedGraph.addNode("C")
        directedGraph.addEdge("A", "B")
        directedGraph.addEdge("C", "A")
        
        then: "incidentEdges应返回正确的边集合"
        def incidentEdges = directedGraph.incidentEdges("A")
        incidentEdges.size() == 2
        incidentEdges.contains(EndpointPair.ordered("A", "B"))
        incidentEdges.contains(EndpointPair.ordered("C", "A"))
        
        when: "对不存在的节点调用incidentEdges"
        directedGraph.incidentEdges("D")
        
        then: "应抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试degree方法计算节点的度
     */
    def "测试degree方法计算节点的度"() {
        given: "创建有向图和无向图"
        def directedGraph = new TestGraph<String>(true, false)
        def undirectedGraph = new TestGraph<String>(false, true)
        
        when: "添加节点和边到有向图"
        directedGraph.addNode("A")
        directedGraph.addNode("B")
        directedGraph.addNode("C")
        directedGraph.addEdge("A", "B")
        directedGraph.addEdge("C", "A")
        
        then: "degree应返回入度和出度之和"
        directedGraph.degree("A") == 2
        directedGraph.degree("B") == 1
        directedGraph.degree("C") == 1
        
        when: "添加节点和边到带自环的无向图"
        undirectedGraph.addNode("A")
        undirectedGraph.addNode("B")
        undirectedGraph.addEdge("A", "B")
        undirectedGraph.addEdge("A", "A") // 自环
        
        then: "degree应返回正确的度数（自环计数两次）"
        undirectedGraph.degree("A") == 3 // 与B的边计1，自环计2
        undirectedGraph.degree("B") == 1
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试inDegree和outDegree方法
     */
    def "测试inDegree和outDegree方法"() {
        given: "创建有向图和无向图"
        def directedGraph = new TestGraph<String>(true, false)
        def undirectedGraph = new TestGraph<String>(false, false)
        
        when: "添加节点和边到有向图"
        directedGraph.addNode("A")
        directedGraph.addNode("B")
        directedGraph.addNode("C")
        directedGraph.addEdge("A", "B")
        directedGraph.addEdge("C", "A")
        directedGraph.addEdge("A", "C")
        
        then: "inDegree和outDegree应返回正确的值"
        directedGraph.inDegree("A") == 1
        directedGraph.outDegree("A") == 2
        directedGraph.inDegree("B") == 1
        directedGraph.outDegree("B") == 0
        directedGraph.inDegree("C") == 1
        directedGraph.outDegree("C") == 1
        
        when: "添加节点和边到无向图"
        undirectedGraph.addNode("A")
        undirectedGraph.addNode("B")
        undirectedGraph.addEdge("A", "B")
        
        then: "无向图的inDegree和outDegree应等于degree"
        undirectedGraph.inDegree("A") == undirectedGraph.degree("A")
        undirectedGraph.outDegree("A") == undirectedGraph.degree("A")
        undirectedGraph.inDegree("B") == undirectedGraph.degree("B")
        undirectedGraph.outDegree("B") == undirectedGraph.degree("B")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试hasEdgeConnecting方法
     */
    def "测试hasEdgeConnecting方法"() {
        given: "创建有向图和无向图"
        def directedGraph = new TestGraph<String>(true, false)
        def undirectedGraph = new TestGraph<String>(false, false)
        
        when: "添加节点和边到有向图"
        directedGraph.addNode("A")
        directedGraph.addNode("B")
        directedGraph.addEdge("A", "B")
        
        then: "hasEdgeConnecting应正确判断边的存在"
        directedGraph.hasEdgeConnecting("A", "B")
        !directedGraph.hasEdgeConnecting("B", "A")
        
        when: "添加节点和边到无向图"
        undirectedGraph.addNode("A")
        undirectedGraph.addNode("B")
        undirectedGraph.addEdge("A", "B")
        
        then: "无向图中边是双向的"
        undirectedGraph.hasEdgeConnecting("A", "B")
        undirectedGraph.hasEdgeConnecting("B", "A")
        
        when: "检查不存在的边"
        directedGraph.hasEdgeConnecting("A", "C")
        
        then: "应返回false"
        !directedGraph.hasEdgeConnecting("A", "C")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试IncidentEdgeSet内部类
     */
    def "测试IncidentEdgeSet内部类"() {
        given: "创建有向图和无向图"
        def directedGraph = new TestGraph<String>(true, true)
        def undirectedGraph = new TestGraph<String>(false, true)
        
        when: "向有向图添加节点和边，包括自环"
        directedGraph.addNode("A")
        directedGraph.addNode("B")
        directedGraph.addEdge("A", "B")
        directedGraph.addEdge("A", "A") // 自环
        
        then: "有向图的incident edges应包含所有相关边"
        def directedIncidentEdges = directedGraph.incidentEdges("A")
        directedIncidentEdges.size() == 2
        directedIncidentEdges.contains(EndpointPair.ordered("A", "B"))
        directedIncidentEdges.contains(EndpointPair.ordered("A", "A"))
        
        when: "向无向图添加节点和边，包括自环"
        undirectedGraph.addNode("A")
        undirectedGraph.addNode("B")
        undirectedGraph.addEdge("A", "B")
        undirectedGraph.addEdge("A", "A") // 自环
        
        then: "无向图的incident edges应包含所有相关边"
        def undirectedIncidentEdges = undirectedGraph.incidentEdges("A")
        undirectedIncidentEdges.size() == 2
        undirectedIncidentEdges.contains(EndpointPair.unordered("A", "B"))
        undirectedIncidentEdges.contains(EndpointPair.unordered("A", "A"))
    }
} 