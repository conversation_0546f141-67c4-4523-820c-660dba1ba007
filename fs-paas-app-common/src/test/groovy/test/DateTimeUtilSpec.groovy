package groovy.test

import com.alibaba.fastjson.JSON
import com.facishare.crm.util.DateTimeUtils
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe
import com.facishare.paas.metadata.util.CopyOnWriteMap
import com.facishare.rest.core.util.JacksonUtil
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR> @date 2019-09-27 16:52
 *
 */
class DateTimeUtilSpec extends Specification {

    DateTimeUtils utils = DateTimeUtils.getInstance()

    String fieldJson = '''{
          "describe_api_name": "SalesOrderProductObj",
          "pattern": "",
          "description": "",
          "is_unique": false,
          "type": "master_detail",
          "is_required": true,
          "define_type": "package",
          "is_single": false,
          "index_name": "string_7",
          "max_length": 256,
          "is_index": true,
          "is_active": true,
          "create_time": 1562081913151,
          "label": "订单",
          "target_api_name": "SalesOrderObj",
          "target_related_list_name": "order_id_list",
          "target_related_list_label": "订单产品",
          "api_name": "order_id",
          "is_create_when_master_create": true,
          "_id": "5d1b7a79a5423cdd263158d9",
          "is_index_field": false,
          "is_required_when_master_create": false,
          "config": {
            "add": 0,
            "edit": 1,
            "enable": 0,
            "display": 1,
            "remove": 0,
            "attrs": {
              "target_related_list_label": 1,
              "cascading_deletion": 0,
              "follow_object_power": 0,
              "is_required": 0,
              "api_name": 0,
              "is_unique": 0,
              "is_create_when_master_create": 0,
              "label": 1,
              "target_api_name": 0,
              "is_required_when_master_create": 0,
              "target_related_list_name": 0,
              "help_text": 1
            }
          },
          "help_text": "",
          "status": "new"
        }'''

    def "remove field from map"() {
        given:
        Map<String, Object> map = JSON.parse(fieldJson)
        IFieldDescribe fieldDescribe = new MasterDetailFieldDescribe(CopyOnWriteMap.copy(map))
        Map<IFieldDescribe, String> defObjMap = Maps.newHashMap()
        defObjMap.put(fieldDescribe, "123")
        IFieldDescribe tmpField = null;
        defObjMap.forEach({ k, v ->
            if (k.getApiName().endsWith("order_id")) {
                tmpField = k;
            }
        })

        expect:
        defObjMap.remove(tmpField) == "123"
        defObjMap.isEmpty() == true
    }

    @Unroll
    def "convert date to timestamp"() {
        expect:
        utils.convertDateToLong(a) == 1569513600000

        where:
        a                     | _
        "2019-9-27"           | _
        "9/27/19"             | _
        "2019/9/27"           | _
        "2019-9-27"           | _
        "2019-09-27 12:12"    | _
        "2019-09-27 12:12:12" | _
        "2019/9/27 12:12"     | _
    }

    @Unroll
    def "convert date time to timestamp"() {
        expect:
        utils.convertDateTimeToLong(a) == b

        where:
        a                    || b
        "2019/9/27 12:12:12" || 1569557532000
        "2019/9/27 12:12"    || 1569557520000    // 没有秒
        "2019/9/27 12:12:12" || 1569557532000
        "2019/9/27 12:12:12" || 1569557532000
    }

    def "test"() {
        expect:
        Set<String> gray = Sets.newHashSet(AppFrameworkConfig.CONFIG_SPLITTER.split("""707337,700932,700933,705167,704077,706013,708434,687889,1,663652,700702,434439,707110,700713,585880,705188,708219,662353,282489,704091,704265,704264,704022,678727,654513,704029,686350,704270,687455,706453,707543,708404,705374,706460,653445,705139,477319,679804,705141,706471,707563,707323,507925,705147,690833,654530,584982,706089,708027,708020,647596,648203,701880,684535,649546,449094,692133,670583,617592,700564,708047,702739,251350,702514,703843,657168,700328,658251,708229,705194,708220,708222,708221,708224,708223,701823,708239,592739,708231,706054,701853,708480,701605,708240,707151,637364,54216,538812,703800,708253,708011,708013,701620,708256,702706,684794,705833,704501,704743,705831,701483,706931,691681,624840,706700,702588,704766,702351,705623,705865,552337,684915,708067,403273,659774,702516,691255,566984,184385,692109,650946,686911,681226,701455,75783,704963,703880,611954,700376,705818,704727,706906,690394,706520,706763,707609,680969,688224,707860,705203,707621,680730,706786,706302,706787,703273,706305,706548,691603,704126,704125,705213,689757,706318,679725,655535,705229,706310,703289,647942,705638,704547,703457,705635,682940,678694,704793,704550,705880,707823,707826,705649,59210,704557,700197,704796,667535,707837,706749,691647,703478,704323,704564,706995,706756,707845,707846,635749,688000,608158,678800,706123,707696,664634,660276,705285,708553,594431,678818,708313,705056,708329,625016,707471,706146,707478,80663,523471,705063,707482,706155,700609,707659,690724,688887,58517,652209,675587,662495,706332,707421,706588,705020,633969,707683,707685,650052,702843,707055,707050,615063,708396,707064,707065,661753,633072,691162,707075,704802,599087,703963,701538,127746,701539,672660,663958,6671,707495,708342,662658,701948,705070,708110,705082,708120,707034,581746,705092,639660,706197,587597,687083,705711,700266,690009,703769,633463,649811,702690,704876,703304,704875,702454,703301,705720,633228,635896,705719,702462,705737,607154,676173,704890,704898,683947,692231,583193,555555,658566,649612,701318,26640,679036,660640,679256,706645,705792,704467,706641,707973,704465,687239,706416,642584,706419,705325,704478,706410,688339,435343,706665,707515,705579,653250,653254,690862,641498,69582,705585,704254,706671,706673,707520,704498,690637,705990,706844,705759,704668,704424,705752,688128,705761,707700,706854,59572,706849,706609,707954,705538,691525,702027,705778,702024,705774,644328,705784,707963,705783,706876,667687,687035,705306,671922,704457,701187,526154,690928,668492,702466,656463,522663,684845,705615,69386,690816,565710,685141,684336,635311,700146,691956,493321,663548,702081,457482,689024,632979,658674,685624,690525,706586,640379,691450,603992,654800,690960,691598,692045,700577,691508,689163,683386,663855,589070,703469,679081,702519,679841,682966,678564,678363,678312,689879,637162,706339,98600,707754,666794,706949,700455,325117,685501,664744,691871,548489,610533,679634,701545,662183,703133,701134,706130,73899,601459,700720,40010021,692099,668075,474881,533020,104211,394234,470621,672895,704983,701513,705208,675188,649619,499206,283197,702042,701021,77539,581070,678548,435155,635958,625938,499243,684778,99153,680181,68403,690097,623333,480123,534501,684807,662474,701873,708049,703252,672352,701865,60891,700080,543786,686174,547877,703802,703241,702183,658336,604371,654228,662182,703260,700370,625892,644870"""))
        Set<String> stage = Sets.newHashSet(AppFrameworkConfig.CONFIG_SPLITTER.split("""663116,590268,608158,590061,590062,590063,663124,663125,679427,679428,590000,590064,700609,590172,590056,590057,663123,663079,663080,590065,706897,706814,706688,706492,706481,706441,706442,706383,706378,706374,706375,706228,706192,706187,706166,705870,705763,705755,705713,705678,683629,679445,679409,679408,679431,679419,663104,663102,663091,663081,663064,663059,663050,663047,662505,662945,662946,662948,662951,683751,662956,662954,683750,662960,662968,683747,662979,683740,683737,665484,683738,683735,683732,683731,683730,683729,683728,683726,683725,683724,683718,683719,683720,683721,683722,683723,683714,683711,683712,683707,683706,683703,683704,683702,683699,683701,683697,683696,683692,683693,683690,683691,683677,683676,683673,683669,683667,683663,683661,663512,683654,683653,683643,683640,683639,683637,683632,683630,683627,683625,679480,679486,679487,679488,679489,679490,679491,679492,679493,679479,679478,679494,679495,665480,679473,679497,679498,679463,679464,663506,679460,679459,663503,679458,679456,663455,679449,679448,679444,679440,679439,679438,679437,679429,679434,679425,679416,679415,679411,679414,679413,679407,679405,679400,679401,679410,665477,663117,663106,663095,663090,663085,663083,663084,663082,663068,663052,663053,663051,663048,663049,663045,663044,663040,663036,663031,663030,663026,663025,665471,663499,663020,663018,665387,663017,663013,663478,663010,663496,663008,663009,663005,663002,662998,662997,662996,662995,662994,662993,663495,662985,662973,662972,662958,662959,662955,662522,662520,662523,662953,662952,662950,662949,662524,662947,662521,662518,662516,662512,589977,589978,589979,589980,589981,589982,589983,589984,589985,589986,589987,589988,589989,589990,589991,589992,589993,589994,589995,589996,589997,589998,589999,590001,590002,590003,590004,590005,590006,590007,590008,590009,590010,590011,590012,590013,590014,590015,590016,590017,590018,590019,590020,590021,590022,590023,590024,590025,590026,590027,590028,590029,590030,590031,590033,590034,590035,590036,590037,590038,590039,590040,590041,590042,590043,590044,590045,590046,590047,590048,590049,590050,590051,590052,590053,590054,590055,590058,590059,590060,590066,590067,590068,590069,590070,590071,590072,590073,590074,590075,590076,590977,590077,590979,590078,590079,590080,590081,590082,590083,590084,590085,590086,590087,590088,590089,590090,590091,590092,590093,590094,590095,590096,590097,590098,590099,590100,590101,590102,590103,590104,590105,590106,590107,590108,590109,590110,590111,590112,590113,590114,590115,590116,590117,590118,590119,590120,590121,590122,590123,590124,590125,590126,590127,590128,590129,590130,590131,590132,590133,590134,590135,590136,590137,590138,590139,590140,590141,590142,590143,590144,590145,590146,590147,590148,590149,590150,590151,590152,590153,590154,590155,590156,590159,590160,590161,590162,590163,590164,590165,590166,590167,590168,590169,590170,590171,590173,590174,590175,590176,590177,590178,590179,590180,590181,590182,590183,590184,590185,590186,590187,590188,590189,590190,590191,590192,590193,590194,590195,590196,590197,590198,590199,590200,590201,590202,590203,590204,590205,590206,590207,590208,590209,590210,590211,590212,590213,590214,590215,590216,590217,590218,590219,590220,590221,590222,590223,590224,590225,590226,590227,590228,590229,590230,590231,590232,590233,590234,590235,590236,590237,590238,590239,590240,590241,590242,590243,590244,590245,590246,590247,590248,590249,590250,590251,590252,590253,590254,590255,590256,590257,590258,590259,590260,590261,590262,590265,590266,590267,590269,590270,590271,590272,590273,590274,590275,707281,707067,707061,707059,707019,706991,706855,706815,706553,706510,706494,706493,706437,706407,706290,706242,706209,706101,706098,706065,705746,705745,705701,705700,705696,665485,683647,679469,679499,683628,679453,679441,679446,679452,663458,679418,679426,679430,663108,663485,663112,663067,663075,663077,663076,663066,663060,663056,663041,663043,663032,663004,662988,662986,662983,662980,683753,683752,683749,683748,683746,683745,662508,683744,662969,662974,683743,662976,683742,683741,683739,663484,683736,683733,683734,683727,683717,683716,683715,683710,683709,683708,683705,683713,683700,683698,665483,683695,683694,683675,683678,683679,683680,683681,683682,683683,683684,683685,683686,683688,683687,663483,683689,683674,663514,670801,683670,683671,683672,683668,683665,683666,663513,683664,663457,683662,683659,683658,683657,683656,683655,683652,665482,683651,683650,683649,683648,683646,683645,683644,683642,683641,683638,683636,683635,683634,683633,663510,663511,683631,663509,683626,683624,679504,679503,679502,679501,663508,663482,679500,679484,679485,679477,679476,679496,679475,679474,679483,679481,679482,679471,663507,679468,679472,679470,679467,663481,679466,679465,679462,679461,663516,665479,663480,663505,665478,679457,663515,679454,679455,679451,679450,679447,679443,679442,679435,679436,679433,679432,679423,679424,679421,679420,679417,679412,663502,679406,679422,679402,679403,679404,663122,663121,663120,663119,663118,663115,663114,663113,663111,663109,663110,663107,663105,663103,663101,663098,663099,663100,663096,663501,663097,663094,663093,663092,663089,663088,663086,663087,663078,663073,663074,663072,663071,663070,663500,663069,665476,663065,663063,663062,663061,663058,663057,663055,663054,663046,663042,663039,663038,663037,665473,663033,663035,663034,663029,663028,663027,665472,663024,663479,663023,663021,663022,663019,665385,663497,665386,663498,663016,663015,663014,663012,663011,663007,663006,663003,663000,663001,662999,662992,662991,662990,663477,662989,662987,665383,662984,662982,662981,662975,662978,662977,662971,663476,662965,662966,662967,662964,662963,662962,662961,662957,662519,662515,662514,662511,662507,662509,662510,662503,662502,662506,155535,705020,706123,702706,700702,705188,706332,705761,688224,705783,700932,706089,706410,701823,704323,706416,687889,700376,691681,705056,706665,706197,706520,706641,706645,705759,706453,705635,705306,701539,706756,705705,705070,706876,706854,703301,688128,633969,705069,705092,680433,702462,705623,704523,705792,700266,706472,702454,707075,703289,706588,706054,705778,706013,704898,704810,705931,706673,705720,707036,705063,688339,701620,702516,703667,700609,704498,707337,701483,704727,706906,705649,705711,706495,704875,704668,703457,704254,706419,705752,705213,690637,704022,705266,705585,705194,706471,705990,704264,650946,706155,707064,706700,705139,705737,707065,660640,706722,700933,703843,706302,706601,706609,707180,705229,705831,690724,705784,702027,706763,704876,706786,705538,707324,706022,671982,706318,705579,662658,686911,705833,705865,703304,704467,704091,704720,705774,703769,707034,705147,706749,703591,704625,705942,706078,706146,706787,704029,706310,704772,707050,706305,704501,705818,706460,704478,702351,705064,707055,703273,706403,706548,701853,705546,703759,704963,704793,704557,686350,700135,705141,706995,701455,690833,678727,680969,706849,706956,705719,706844,704267,705638,702191,704743,706358,650943,702690,706516,701538,689519,706598,704077,701880,704802,704890,707110,707030,704766,706402,701536,687083,707031,615063,705779,704547,688763,700342,704706,706599,705374,678800,662495,706534,706817,701209,706467,706984,707151,703963,700197,705203,705535,704790,707421,704564,703602,704125,635896,684938,691647,703124,705285,684847,701210,704477,705016,704709,704265,703676,691162,700608,704951,664634,700685,705542,704270,707371,704616,705951,707323,704796,690862,700945,633072,523471,58517,649612,26640,592739,80663,583193,584982,127746,54216,585880,59572,59210,477319,552337,282489,75783,594431,617592,648203,653445,649811,652209,587597,653250,655535,657168,667687,647942,658251,658566,659774,660276,644328,69582,663652,663958,647596,672660,676173,679804,667535,633228,661753,607154,650052,671922,688000,684535,701605,184385,6671,633347,1,538812,331581,577031,671268,411314,603952,654530,575210,671581,595066,682646,647486,577932,678558,71452,680940,449094,658069,548442,562839,606615,592172,452276,675188,382174,613122,679841,623269,679360,677183,684152,447504,430464,488230,392829,458227,477484,624844,597803,672397,565119,501903,642103,341999,626695,670511,364298,617897,610533,640379,654245,666077,620878,641498,705804,470621,561656,434439,664744,703505,604371,691496,690960,642332,683386,706586,670529,677058,663636,479611,581070,662057,648359,678548,690957,554435,650282,238266,663910,175847,477689,625016,71880,651054,661268,703625,540412,688078,52807,403273,519574,703638,704457,675804,183950,273602,213776,414206,603704,362406,603018,275814,391017,560452,689568,248839,330853,701545,503528,44531,682661,546984,557052,521009,647557,680415,608580,72777,442120,638131,104211,597993,627891,599112,241682,704492,678363,700479,701468,685477,621369,690009,579205,678312,425781,600066,540265,633659,545361,508682,57562,705968,365025,535822,679794,560176,499438,432897,599297,685120,607699,592325,679953,261918,642374,701916,638129,555231,559775,488308,635139,626876,690031,633731,597881,681423,554419,553842,702183,63559,472713,429928,439248,541743,636760,657231,417501,643924,680357,462767,388320,689760,704247,338942,155910,646301,489466,604515,546711,86796,666651,706753,623333,683415,705265,624924,691025,88315,685257,474881,683204,705858,679647,688228,690958,700455,700504,628713,679051,470292,600153,700391,340141,683234,684326,701843,672301,683000,400808,685206,703777,565375,642935,703793,603203,536983,462864,690823,393117,475673,675829,595512,631728,619212,74889,669584,288506,684773,550878,665505,353888,680938,587124,229901,701948,621669,533768,704321,662338,688252,676370,401346,672648,568906,401683,526271,570456,691575,369354,362046,572155,673432,686822,666329,432154,88103,685279,646634,705040,643966,533575,539616,385793,701824,632361,97672,610890,405313,703133,566980,473048,630930,382316,700156,303418,393545,617328,510701,691753,672353,690773,656652,655000,664226,603560,702514,670800,670801,671149,675188,237970"""))
        Set<String> tenantId = Sets.newHashSet(AppFrameworkConfig.CONFIG_SPLITTER.split("""1,705020,706123,700702,706089,706332,702706,691681,688224,706876,705188,707034,687889,705761,706520,688339,690637,700376,706854,704323,707609,700932,690862,701539,706410,686350,633969,705783,700266,690724,706763,706665,700135,705649,701823,680433,706146,706641,662658,706310,706416,706756,684938,705056,701483,702191,706906,650946,707110,705306,702024,706786,703301,708240,708047,701536,701187,705635,703289,701620,702462,707700,705070,702351,701455,706197,707696,701880,702516,691502,702514,686911,708239,706453,707563,706645,708975,708224,704668,707973,688128,705778,678727,705759,691647,704467,708625,683947,708120,705784,705092,705585,671982,708396,684438,705623,700609,702454,708976,707495,707337,701209,705069,705711,705147,707075,705792,704498,703769,660641,704523,708027,704898,705063,702027,704875,708885,706013,700933,680969,707323,706588,615063,700197,705194,708816,708313,681118,707683,709314,691162,704706,706054,687035,709095,705229,708750,704501,703667,704810,701538,681119,708787,709251,705990,705931,700342,708969,687455,704727,708404,689519,704022,700685,709238,685166,709201,703273,660640,704254,705720,707659,706460,706155,703457,706700,704264,703963,705737,707543,684847,709324,664634,707826,707482,678800,704720,645230,708020,650943,707065,705638,704876,706419,705266,704091,709077,705752,706471,681116,635896,687094,705538,708220,708714,700232,705831,709477,709463,704766,709169,700945,705213,708616,703304,708553,687083,704625,703843,705579,707064,708223,704265,690833,707478,706609,708229,706302,704743,709476,709181,704029,691603,706318,708699,708253,681117,701853,704478,706548,704126,708753,702147,706787,705865,704125,705774,706849,705285,704077,709264,707050,708758,707823,704267,704793,704796,704547,705064,709008,705833,707954,709548,707837,701210,704270,703124,706305,707520,703602,704802,702690,709076,707055,703591,706844,705942,706749,704256,704260,708892,688763,704557,704778,709032,708930,704448,708110,709369,705167,675334,690394,708434,708221,708887,704556,709489,709154,704545,708342,704564,705374,700608,707845,705203,708644,706403,703759,669944,708842,704616,705546,709483,705719,704951,708222,690829,702731,706995,708013,690834,684940,707963,704542,705542,708231,685165,708738,708763,662495,701537,704465,706358,709336,706402,707621,708940,706516,706598,708689,705067,708329,707860,709067,709272,615009,707151,707846,704470,706467,709137,708607,705779,707685,706599,706931,707719,703615,708671,709097,705951,704794,707421,709089,709282,709367,709132,615007,662240,704477,705082,708011,708947,704709,709474,680255,690442,703880,644328,617592,69582,641883,658566,54216,523471,26640,633072,653445,647596,663958,583193,594431,679804,653250,59210,552337,633228,657168,6671,660276,127746,649612,584982,80663,282489,659774,592739,58517,658251,585880,647942,652209,618752,663652,587597,672660,75783,667687,607154,649811,59572,650052,688000,707510,477319,701605,684535,648203,655535,676173,496004,633347"""))
        println gray
        println stage
        println tenantId

        println gray.size()

//        gray.removeAll(stage)
        gray.removeAll(tenantId)

        println gray.size()

        println String.join(",", gray)

    }

    def "test 2"() {
        expect:
        List<String> ei = Lists.newArrayList(AppFrameworkConfig.CONFIG_SPLITTER.split("""549366,381492,633975,570967,
611667,568645,670625,455014,606457,663598,632829,635329,542661,481495,498044,501449,598335,99598,199529,526380,86506,570814,627115,489207,597902,90706,678825,701872,633761,85421,397315,389877,606802,595995,577382,642824,636965,585375,624115,496991,322246,562487,542956,564174,631171,642294,597846,511164,597566,701560,604891,111024,78722,649328,77617,339525,615315,678793,57515,650993,61456,560528,331894,642028,488989,59472,1,55002,49148,507719,256184,448142,102177,419804,701878,35187,32099,6030,127914,17824,632477,656230,658399,632768,577152,629927,488995,687979,580984,648162,324519,541219,678424,424059,82897,491187,674400,619615,7689,589227,629257,592135,603207,672394,617168,565556,671597,588110,642812,210294,556015,673842,678719,688092,684567,682722,680935,683002,678447,677184,623264,678845,671260,678853,472252,535673,635758,622836,200501,646222,636781,307447,664226,626246,324450,590193,689107,461402,595453,662757,503575,12,581718,594192,598144,643224"""))
        def map = ei.collectEntries { value ->
            [(value): ei.indexOf(value)]
        }
        println map
        println JacksonUtil.toJson(map)
        1 == 1
    }
}
