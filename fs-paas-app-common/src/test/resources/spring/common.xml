<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>

    <bean id="orgServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.OrgServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="sendCrmMessageProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.SendCrmMessageProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="sendEmailProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.SendEmailProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="servicePersonnelSearchSettingProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.crm.service.ServicePersonnelSearchSettingProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>


        <bean id="objectSearchServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.ObjectSearchServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="messageBoxServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.MessageBoxServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="sendCalculateJobProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.JobScheduleProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="networkDiskProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.common.service.NetworkDiskProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>
    <import resource="privilege-dubbo.xml"/>
</beans>