<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 设置特定包的日志级别 -->
    <logger name="com.github.trace" level="ERROR"/>
    <logger name="com.facishare" level="INFO"/>
    <logger name="com.github.autoconf" level="ERROR"/>
    <logger name="org.apache.commons.beanutils" level="ERROR"/>
    <logger name="org.apache.commons" level="ERROR"/>
    <logger name="org.apache" level="ERROR"/>
    <logger name="org.springframework" level="ERROR"/>
    <logger name="org.mybatis" level="ERROR"/>
    <logger name="org.hibernate" level="ERROR"/>
    <logger name="com.alibaba" level="ERROR"/>
    <logger name="com.github" level="ERROR"/>
    <logger name="io.netty" level="ERROR"/>
    <logger name="org.jboss" level="ERROR"/>
    <logger name="org.apache.zookeeper" level="ERROR"/>
    <logger name="org.apache.dubbo" level="ERROR"/>
    <logger name="com.netflix" level="ERROR"/>

    <!-- 根日志级别 -->
    <root level="WARN">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration> 